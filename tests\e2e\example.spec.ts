import { test, expect } from "@playwright/test";

test.describe("Landing Page", () => {
  test("should render and display main heading", async ({ page }) => {
    await page.goto("/");
    await expect(
      page.getByRole("heading", { name: "PxlMorph AI" }),
    ).toBeVisible();
    await expect(
      page.getByText(
        "Unleash your creativity with AI-powered art. Stunning visuals, effortless creation.",
      ),
    ).toBeVisible();
    await expect(
      page.getByRole("link", { name: "Enter Studio" }),
    ).toBeVisible();
  });

  test("should navigate to studio when clicking Enter Studio", async ({
    page,
  }) => {
    await page.goto("/");
    await page.getByRole("link", { name: "Enter Studio" }).click();
    await expect(page).toHaveURL(/\/studio/);
    // Check for either Studio header or login form
    const studioHeader = page.getByRole("heading", { name: /Studio/i });
    const signInTab = page.getByRole("tab", { name: /Sign In/i });
    if (await studioHeader.isVisible({ timeout: 2000 })) {
      await expect(studioHeader).toBeVisible();
    } else {
      await expect(signInTab).toBeVisible();
    }
  });
});
