// app/api/edit-image/route.ts
import { NextRequest, NextResponse } from "next/server";
import { editImageHandler } from "@/lib/api/editImageHandler";

export async function POST(request: NextRequest) {
  const requestHeaders = Object.fromEntries(request.headers.entries());
  const formDataRaw = await request.formData();
  // Convert FormData to plain object for handler
  const formData: Record<string, any> = {};
  for (const [key, value] of formDataRaw.entries()) {
    formData[key] = value;
  }
  const env = process.env;
  // OpenAI and Supabase should be passed in real route, but for now pass undefined (handler will error if not set)
  const result = await editImageHandler({
    headers: requestHeaders,
    formData,
    env,
  });
  return NextResponse.json(result.body, { status: result.status });
}
