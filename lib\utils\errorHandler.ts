import { toast } from "sonner";

/**
 * Standardized error handler for API and UI errors.
 * Logs, reports, and shows user feedback.
 * Usage: errorHandler(error, { userMessage: 'Something went wrong' })
 */
export function errorHandler(
  error: unknown,
  options?: { userMessage?: string; logOnly?: boolean }
) {
  // Log error to console
  if (process.env.NODE_ENV === "development") {
    console.error(error);
  }
  // TODO: Add error reporting (e.g., Sentry) here if needed

  // Show user feedback unless logOnly
  if (!options?.logOnly) {
    toast.error(options?.userMessage || "An unexpected error occurred");
  }
} 