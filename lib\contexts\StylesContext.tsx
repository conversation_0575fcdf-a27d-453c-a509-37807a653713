
"use client";

import React, { createContext, useContext, ReactNode, useMemo } from "react";
import { Style } from "@/lib/types";
import useSWR from "swr";

const fetcher = (url: string) => fetch(url).then((res) => res.json());

// Context interface
interface StylesContextType {
  styles: Style[];
  isLoading: boolean;
  error: Error | null;
  mutate: () => void; // Keep mutate for potential future dynamic data
}

// Create context
const StylesContext = createContext<StylesContextType | undefined>(undefined);

// Provider component
export function StylesProvider({ children }: { children: ReactNode }) {
  // Use SWR to fetch styles data
  const { data: stylePromptsRaw, error, isLoading, mutate } = useSWR('/api/get-styles', fetcher);

  const styles: Style[] = useMemo(() => {
    if (!stylePromptsRaw) return [];
    
    const flatStyles: Style[] = [];
    Object.entries(stylePromptsRaw).forEach(([category, categoryStyles]: [string, any]) => {
      if (Array.isArray(categoryStyles)) {
        categoryStyles.forEach((style: any) => {
          flatStyles.push({
            id: style.id,
            name: style.title,
            description: style.description, // Assuming description is present
            prompt: style.prompt,
            example_image: style.example_image,
            category: category,
          });
        });
      }
    });
    return flatStyles;
  }, [stylePromptsRaw]);

  const value: StylesContextType = {
    styles: styles,
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };

  return (
    <StylesContext.Provider value={value}>{children}</StylesContext.Provider>
  );
}

// Hook to use the styles context
export function useStyles() {
  const context = useContext(StylesContext);
  if (context === undefined) {
    throw new Error("useStyles must be used within a StylesProvider");
  }
  return context;
}

// Hook for getting a specific style by ID
export function useStyle(styleId: string) {
  const { styles, isLoading, error } = useStyles();
  const style = styles.find((s) => s.id === styleId);

  return {
    style,
    isLoading,
    error,
    exists: !!style,
  };
}

// Hook for getting styles by category
export function useStylesByCategory(category: string) {
  const { styles, isLoading, error } = useStyles();
  const filteredStyles = styles.filter((s) => s.category === category);

  return {
    styles: filteredStyles,
    isLoading,
    error,
    count: filteredStyles.length,
  };
}
