# 🏆 Image Display Optimization Summary

## 1. SWR for Display URL Fetching

- All components that display images (`UnifiedImageCard`, `UnifiedImageModal`, etc.) use [SWR](https://swr.vercel.app/) to fetch and cache the display URL for each image.
- The SWR key is always `[image.id, image.image_url, 'cached']`, ensuring deduplication across all usages.

**Example:**

```js
const fetchCachedUrl = async () => {
  const url = await ImageUrlHandler.getCachedImageUrl(
    image.id,
    image.image_url,
  );
  if (url) return url;
  throw new Error("Failed to get cached image URL");
};
const {
  data: swrDisplayUrl,
  error: swrError,
  isLoading: swrLoading,
} = useSWR([image.id, image.image_url, "cached"], fetchCachedUrl, {
  revalidateOnFocus: false,
});
```

---

## 2. In-Flight Request Deduplication

- The backend call for a signed URL is deduplicated using an in-memory map in `<PERSON><PERSON>rl<PERSON>and<PERSON>`.
- If multiple requests for the same image are made at the same time, only one backend call is made; all callers receive the same result.

---

## 3. Cache Hierarchy

- **IndexedDB**: Persistent cache for signed URLs and image blobs.
- **In-memory cache**: For the current session.
- **SWR cache**: For React component-level deduplication and reactivity.

---

## 4. Where This Is Used

- **Gallery Page**
- **Community Gallery**
- **Recent Creations**
- **Image Modals**
- **Anywhere you use `UnifiedImageCard` or `UnifiedImageModal`**

---

## 5. Benefits

- **No duplicate backend/API calls** for the same image.
- **No duplicate computation** for display URLs.
- **Fast, efficient, and scalable** for large galleries.
- **Easy to extend**: Just use the same SWR pattern and key for any new image display component.

---

## 6. How to Extend

- For any new image display, use:
  ```js
  useSWR([image.id, image.image_url, "cached"], fetchCachedUrl, {
    revalidateOnFocus: false,
  });
  ```
- Or, use the existing `UnifiedImageCard`/`UnifiedImageModal` components.

---

**You now have a best-practice, production-grade, deduplicated, and cached image display system across your entire app!**
