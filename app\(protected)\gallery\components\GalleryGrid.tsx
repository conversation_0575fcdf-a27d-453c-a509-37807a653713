"use client";

import React, { useMemo, useCallback } from "react";
import { ImageGrid } from "@/components/gallery/ImageGrid";
import { GeneratedImage } from "@/lib/types";
import { useUser } from "@/lib/contexts/UserContext";

interface GalleryGridProps {
  filteredImages: Array<GeneratedImage>;
  onShare?: (imageId: string, isShared: boolean) => void;
  onDelete?: (imageId: string) => void;
  isLoading?: boolean;
  error?: string | null;
  loadMore?: () => void;
  hasMore?: boolean;
}

export const GalleryGrid = React.memo(function GalleryGrid({
  filteredImages,
  onShare,
  onDelete,
  isLoading,
  error,
  loadMore,
  hasMore,
}: GalleryGridProps) {
  const { user } = useUser();
  
  // Stable isImageOwner function
  const isImageOwner = useCallback((image: GeneratedImage) => 
    Boolean(user?.id && image.user_id === user.id), 
    [user?.id]
  );

  // Stable onShare handler
  const handleShare = useMemo(() => {
    if (!onShare) return undefined;
    return () => (imageId: string, isShared: boolean) => onShare(imageId, isShared);
  }, [onShare]);

  // Stable onDelete handler
  const handleDelete = useCallback((imageId: string) => {
    if (filteredImages.find((img) => img.id === imageId) && isImageOwner(filteredImages.find((img) => img.id === imageId)!) && onDelete) {
      onDelete(imageId);
    }
  }, [filteredImages, isImageOwner, onDelete]);

  // Memoize the ImageGrid props to prevent unnecessary re-renders
  const imageGridProps = useMemo(() => ({
    images: filteredImages,
    isLoading,
    error,
    loadMore,
    hasMore,
    onShare: handleShare,
    onDelete: handleDelete,
    showDownloadButton: true,
    showSharedChip: true,
    hideLikeButton: true,
  }), [
    filteredImages,
    isLoading,
    error,
    loadMore,
    hasMore,
    handleShare,
    handleDelete
  ]);

  return <ImageGrid {...imageGridProps} />;
});
