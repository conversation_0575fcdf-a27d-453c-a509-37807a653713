# ✅ TODO 

- [x] Dynamic import for Framer Motion and Sonner (animations and notifications)
- [x] Ensure only one Toaster (<PERSON><PERSON>) is rendered globally to prevent duplicate toasts
- [] Use dynamic imports for any other heavy or rarely-used components (e.g., modals, charts, admin tools)
- [x] Audit and remove unused dependencies from package.json
- [ ] Confirm LCP image is optimized and prioritized (using next/image with priority if above the fold)
- [ ] Keep Next.js and React up to date for latest performance improvements
- [ ] Regularly run Lighthouse and bundle analyzer after major changes