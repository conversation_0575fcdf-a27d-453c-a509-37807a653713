"use client";
import { usePathname } from "next/navigation";
import { useEffect } from "react";

export default function ScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    // Try scrolling the main window
    window.scrollTo({ top: 0, left: 0, behavior: "auto" });

    // Try scrolling html and body
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;

    // Try scrolling main containers
    const siteRoot = document.getElementById("site-root");
    if (siteRoot) {
      siteRoot.scrollTop = 0;
      siteRoot.scrollLeft = 0;
    }
    const mainScroll = document.getElementById("main-scroll");
    if (mainScroll) {
      mainScroll.scrollTop = 0;
      mainScroll.scrollLeft = 0;
    }
  }, [pathname]);

  return null;
} 