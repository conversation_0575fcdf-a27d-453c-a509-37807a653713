{"numFailedTestSuites": 1, "numFailedTests": 1, "numPassedTestSuites": 0, "numPassedTests": 1, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 2, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1752774831508, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Minimal Integration Test"], "duration": 14, "failing": false, "failureDetails": [], "failureMessages": [], "fullName": "Minimal Integration Test should render the mock component", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "startAt": 1752774832620, "status": "passed", "title": "should render the mock component"}, {"ancestorTitles": ["Minimal Integration Test"], "duration": 44, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\n    at createFiberFromTypeAndProps (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:28439:17)\n    at createFiberFromElement (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:28465:15)\n    at reconcileSingleElement (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:15750:23)\n    at reconcileChildFibers (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:15808:35)\n    at reconcileChildren (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:19167:28)\n    at updateHostComponent (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:19924:3)\n    at beginWork (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:21618:14)\n    at beginWork$1 (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:27426:14)\n    at performUnitOfWork (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:26560:12)\n    at workLoopSync (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:26466:5)\n    at renderRootSync (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:26434:7)\n    at recoverFromConcurrentError (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:25850:20)\n    at performConcurrentWorkOnRoot (C:\\CODING\\PxlMorph\\node_modules\\react-dom\\cjs\\react-dom.development.js:25750:22)\n    at flushActQueue (C:\\CODING\\PxlMorph\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (C:\\CODING\\PxlMorph\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at C:\\CODING\\PxlMorph\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (C:\\CODING\\PxlMorph\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (C:\\CODING\\PxlMorph\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (C:\\CODING\\PxlMorph\\tests\\integration\\like-modal-share.integration.test.tsx:92:35)\n    at Promise.finally.completed (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at _runTest (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (C:\\CODING\\PxlMorph\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (C:\\CODING\\PxlMorph\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (C:\\CODING\\PxlMorph\\node_modules\\jest-runner\\build\\index.js:340:7)"], "fullName": "Minimal Integration Test should render the mocked UnifiedImageCard", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1752774832635, "status": "failed", "title": "should render the mocked UnifiedImageCard"}], "endTime": 1752774832685, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mMinimal Integration Test › should render the mocked UnifiedImageCard\u001b[39m\u001b[22m\n\n    Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 90 |\u001b[39m     \u001b[22m\n\u001b[2m     \u001b[90m 91 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 92 |\u001b[39m       \u001b[36mconst\u001b[39m { container } \u001b[33m=\u001b[39m render(\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 93 |\u001b[39m         \u001b[33m<\u001b[39m\u001b[33mdiv\u001b[39m data\u001b[33m-\u001b[39mtestid\u001b[33m=\u001b[39m\u001b[32m\"test-container\"\u001b[39m\u001b[33m>\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 94 |\u001b[39m           \u001b[33m<\u001b[39m\u001b[33mUnifiedImageCard\u001b[39m image\u001b[33m=\u001b[39m{mockImage} \u001b[33m/\u001b[39m\u001b[33m>\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 95 |\u001b[39m         \u001b[33m<\u001b[39m\u001b[33m/\u001b[39m\u001b[33mdiv\u001b[39m\u001b[33m>\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat createFiberFromTypeAndProps (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:28439:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat createFiberFromElement (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:28465:15)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileSingleElement (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15750:23)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileChildFibers (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15808:35)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat reconcileChildren (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:19167:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat updateHostComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:19924:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21618:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27426:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26560:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26466:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26434:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25850:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25750:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/integration/like-modal-share.integration.test.tsx\u001b[39m\u001b[0m\u001b[2m:92:35)\u001b[22m\u001b[2m\u001b[22m\n", "name": "C:\\CODING\\PxlMorph\\tests\\integration\\like-modal-share.integration.test.tsx", "startTime": 1752774832055, "status": "failed", "summary": ""}], "wasInterrupted": false}