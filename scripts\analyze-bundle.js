#!/usr/bin/env node

import { execSync } from "child_process";
import fs from "fs";
import path from "path";

console.log("🔍 Analyzing bundle size...\n");

try {
  // Build the project
  console.log("📦 Building project...");
  execSync("npm run build", { stdio: "inherit" });

  // Run bundle analyzer
  console.log("\n📊 Running bundle analyzer...");
  execSync("npx @next/bundle-analyzer .next/static/chunks", {
    stdio: "inherit",
  });

  // Generate bundle report
  console.log("\n📋 Generating bundle report...");
  const reportPath = path.join(process.cwd(), "bundle-report.json");

  // Get bundle stats
  const stats = {
    timestamp: new Date().toISOString(),
    totalSize: 0,
    chunks: [],
    recommendations: [],
  };

  // Analyze chunks directory
  const chunksDir = path.join(process.cwd(), ".next/static/chunks");
  if (fs.existsSync(chunksDir)) {
    const files = fs.readdirSync(chunksDir);

    files.forEach((file) => {
      if (file.endsWith(".js")) {
        const filePath = path.join(chunksDir, file);
        const stats = fs.statSync(filePath);
        const sizeInKB = (stats.size / 1024).toFixed(2);

        console.log(`📄 ${file}: ${sizeInKB} KB`);

        if (stats.size > 500 * 1024) {
          // 500KB threshold
          stats.recommendations.push(
            `Consider code splitting for ${file} (${sizeInKB} KB)`,
          );
        }
      }
    });
  }

  // Save report
  fs.writeFileSync(reportPath, JSON.stringify(stats, null, 2));
  console.log(`\n✅ Bundle report saved to: ${reportPath}`);
} catch (error) {
  console.error("❌ Bundle analysis failed:", error.message);
  process.exit(1);
}

console.log("\n🎉 Bundle analysis complete!");
console.log("\n💡 Optimization tips:");
console.log("• Use dynamic imports for large components");
console.log("• Implement code splitting for routes");
console.log("• Optimize images with next/image");
console.log("• Remove unused dependencies");
console.log("• Consider tree shaking for better bundle size");
