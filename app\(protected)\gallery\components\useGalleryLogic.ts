import { useState, useEffect, useCallback, useMemo } from "react";
import { useUser } from "@/lib/contexts/UserContext";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { useImageSharing } from "@/lib/hooks/useImageSharing";
import { useAllUserImages } from "@/lib/hooks/useAllUserImages";
import { useGeneration } from "@/lib/contexts/GenerationContext";

export function useGalleryLogic() {
  const { styles } = useStyles();
  const { user } = useUser();
  const { trackSearch, trackFilterUsage } = useAnalytics();
  const { handleImageShare: baseHandleImageShare } = useImageSharing();
  const { images, setImages, isLoading, error, mutate, hasMore, loadMore } = useAllUserImages(user?.id);
  const [searchQuery, setSearchQuery] = useState("");
  const [styleFilter, setStyleFilter] = useState("all");
  const [shareFilter, setShareFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"all" | "grouped">("all");
  const { isGenerating, setIsGenerating, lastGeneratedImage } = useGeneration();
  // Filtered images based on search, style, and share filters
  const filteredImages = useMemo(() => {
    if (!Array.isArray(images) || images.length === 0) return [];
    let filtered = [...images];
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((image) =>
        image.style?.toLowerCase().includes(query)
      );
    }
    if (styleFilter && styleFilter !== "all") {
      filtered = filtered.filter((image) => image.style === styleFilter);
    }
    if (shareFilter && shareFilter !== "all") {
      const isShared = shareFilter === "shared";
      filtered = filtered.filter((image) => image.is_shared === isShared);
    }
    return filtered;
  }, [images, searchQuery, styleFilter, shareFilter]);

  // Share handler using SWR mutate and optimistic updates
  const handleImageShare = useCallback(
    async (imageId: string, isShared: boolean) => {
      if (!user?.id) return;
      
      // Optimistically update the local state
      setImages(currentImages => 
        (currentImages || []).map(img => 
          img.id === imageId ? { ...img, is_shared: isShared } : img
        )
      );
      
      const success = await baseHandleImageShare(imageId, isShared);
      
      if (!success) {
        // Revert on failure
        setImages(currentImages => 
          (currentImages || []).map(img => 
            img.id === imageId ? { ...img, is_shared: !isShared } : img
          )
        );
      }
      
      // Revalidate to sync with the server
      mutate();
    },
    [baseHandleImageShare, user?.id, mutate, setImages]
  );

  // Delete handler using SWR mutate
  const handleImageDelete = useCallback(
    async () => {
      // No-op: deletion is now handled in UnifiedImageCard. Optionally, use for analytics or UI notification.
      // Example: trackEvent('image_delete', { imageId });
    },
    []
  );

  // Search and filter handlers
  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQuery(query);
      if (query.trim()) {
        trackSearch(query, "gallery", filteredImages.length);
      }
    },
    [filteredImages.length, trackSearch]
  );

  const handleStyleFilterChange = useCallback(
    (filter: string) => {
      if (!filter || (styles && !styles.find((s) => s.id === filter) && filter !== "all")) {
        console.warn("Invalid style filter:", filter);
        return;
      }
      setStyleFilter(filter);
    },
    [styles]
  );

  const handleShareFilterChange = useCallback((filter: string) => {
    if (!filter || !["all", "shared", "private"].includes(filter)) {
      console.warn("Invalid share filter:", filter);
      return;
    }
    setShareFilter(filter);
  }, []);

  // Track filter usage
  useEffect(() => {
    if (styleFilter && styleFilter !== "all") {
      trackFilterUsage("style", styleFilter, "gallery");
    }
  }, [styleFilter, trackFilterUsage]);

  useEffect(() => {
    if (shareFilter && shareFilter !== "all") {
      trackFilterUsage("share", shareFilter, "gallery");
    }
  }, [shareFilter, trackFilterUsage]);

  useEffect(() => {
    function handleImageCreated() {
      mutate();
    }
    window.addEventListener("imageCreated", handleImageCreated);
    return () => {
      window.removeEventListener("imageCreated", handleImageCreated);
    };
  }, [mutate]);

  useEffect(() => {
    if (isGenerating && lastGeneratedImage) {
      // If the generated image is now in the gallery, stop generating
      if (images && images.some(img => img.id === lastGeneratedImage.id)) {
        setIsGenerating(false);
      }
    }
  }, [isGenerating, lastGeneratedImage, images, setIsGenerating]);

  return useMemo(
    () => ({
      images,
      isLoading,
      error,
      filteredImages,
      searchQuery,
      setSearchQuery,
      styleFilter,
      setStyleFilter,
      shareFilter,
      setShareFilter,
      viewMode,
      setViewMode,
      handleImageShare,
      handleImageDelete,
      styles,
      handleSearchChange,
      handleStyleFilterChange,
      handleShareFilterChange,
      loadMore,
      hasMore,
      isGenerating,
    }),
    [
      images,
      isLoading,
      error,
      filteredImages,
      searchQuery,
      styleFilter,
      shareFilter,
      viewMode,
      handleImageShare,
      handleImageDelete,
      styles,
      handleSearchChange,
      handleStyleFilterChange,
      handleShareFilterChange,
      isGenerating,
      loadMore,
      hasMore,
    ]
  );
}
