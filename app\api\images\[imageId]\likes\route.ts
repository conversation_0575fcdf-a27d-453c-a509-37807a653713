import { NextRequest, NextResponse } from "next/server";
import { imageLikesHandler } from "@/lib/api/imageLikesHandler";

export async function GET(
  request: NextRequest,
  context: { params: { imageId: string } },
) {
  const { imageId } = await context.params;
  const { status, body } = await imageLikesHandler({
    imageId,
    headers: request.headers,
  });
  return NextResponse.json(body, { status });
}
