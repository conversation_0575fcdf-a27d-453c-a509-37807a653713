"use client";
import { useCallback, useState } from "react";
import { showGenerationToast } from "@/lib/utils/toast";
import { errorHandler } from "@/lib/utils/errorHandler";
import { supabase } from "@/lib/supabase";
import { useGeneration } from "@/lib/contexts/GenerationContext";
import { User, GeneratedImage } from "@/lib/types";

interface Style {
  id: string;
  name: string;
  description?: string;
  example_image?: string;
  category: string;
}

interface UseImageGenerationParams {
  user: User | null;
  selectedStyle: string;
  uploadedImage: File | null;
  customPrompt: string;
  trackImageGeneration: (style: string) => void;
  trackEvent: (event: string, data: Record<string, unknown>) => void;
  addRecentlyUsedStyle: (style: Style) => Promise<void>;
  allStyles: Style[];
  refreshRecentImages: () => Promise<void>;
  loadImageFromIndexedDb: () => void;
}

/**
 * useImageGeneration - Hook for image generation logic, validation, API, analytics, and toast feedback.
 * @param {UseImageGenerationParams} params - Parameters for the hook.
 * @returns {Object} { isGenerating, generationProgress, generateImage }
 */
export function useImageGeneration({
  user,
  selectedStyle,
  uploadedImage,
  customPrompt,
  trackImageGeneration,
  trackEvent,
  addRecentlyUsedStyle,
  allStyles,
  refreshRecentImages,
  loadImageFromIndexedDb,
}: UseImageGenerationParams) {
  const { setIsGenerating } = useGeneration();
  const [isGeneratingLocal, setIsGeneratingLocal] = useState(false);
  const [generationProgress, setGenerationProgress] = useState("");

  const generateImage = useCallback(async () => {
    if (!user) {
      showGenerationToast('error');
      return;
    }
    setIsGeneratingLocal(true);
    setIsGenerating(true);
    setGenerationProgress("Preparing your request...");
    if (!selectedStyle) {
      showGenerationToast('error');
      setIsGeneratingLocal(false);
      setIsGenerating(false);
      setGenerationProgress("");
      return;
    }
    if (!uploadedImage) {
      showGenerationToast('error');
      setIsGeneratingLocal(false);
      setIsGenerating(false);
      setGenerationProgress("");
      return;
    }
    showGenerationToast('start'); // Show toast immediately
    try {
      setGenerationProgress("🎨 Generating your masterpiece...");
      trackImageGeneration(selectedStyle);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error("Authentication session not found. Please sign in again.");
      setGenerationProgress("🤖 Calling AI service...");
      const formData = new FormData();
      formData.append("styleId", selectedStyle);
      if (customPrompt) formData.append("customPrompt", customPrompt);
      if (uploadedImage) formData.append("uploadedImage", uploadedImage);
      const response = await fetch("/api/generate-image", {
        method: "POST",
        headers: { Authorization: `Bearer ${session.access_token}` },
        body: formData,
      });
      const responseData = await response.json();
      if (!response.ok) {
        let backendError = responseData.error;
        if (responseData.details) backendError += ": " + JSON.stringify(responseData.details);
        if (response.status === 503) throw new Error("Network connectivity issue detected. This may be due to environment restrictions. Please try again or contact support.");
        if (response.status === 408) throw new Error("Request timed out. Please try a shorter prompt and try again.");
        if (response.status === 429) throw new Error("Rate limit exceeded. Please wait a moment and try again.");
        if (backendError && backendError.includes("OpenAI Safety System")) {
          showGenerationToast('blocked');
          trackEvent("moderation_block", {
            file_type: uploadedImage?.type,
            file_size: uploadedImage?.size,
            timestamp: new Date().toISOString(),
          });
        }
        throw new Error(backendError || "Failed to generate image");
      }
      setGenerationProgress("💾 Saving your artwork...");
      if (responseData.imageData) {
        await refreshRecentImages();
        const { mutate } = await import("swr");
        if (user?.id) {
          mutate([
            "all-user-images",
            user.id,
          ], (images: GeneratedImage[] = []) => {
            if (images.some((img) => img.id === responseData.imageData.id)) return images;
            return [responseData.imageData, ...images];
          }, false);
        }
        if (typeof window !== "undefined" && user?.id) {
          window.dispatchEvent(new CustomEvent("imageCreated", { detail: { image: responseData.imageData, userId: user.id } }));
        }
        loadImageFromIndexedDb();
        // Add to recently used styles
        const styleObj = allStyles.find((s) => s.id === selectedStyle);
        if (styleObj) {
          await addRecentlyUsedStyle({
            id: styleObj.id,
            name: styleObj.name,
            example_image: styleObj.example_image || "",
            createdAt: new Date().toISOString(),
          });
        }
        if (responseData.warning) {
          showGenerationToast('success');
        } else {
          showGenerationToast('saved');
        }
      } else {
        showGenerationToast('error');
      }
    } catch (error) {
      errorHandler(error, { userMessage: "Failed to generate image" });
      showGenerationToast('error');
    } finally {
      setIsGeneratingLocal(false);
      setIsGenerating(false);
      setGenerationProgress("");
    }
  }, [user, selectedStyle, uploadedImage, customPrompt, trackImageGeneration, trackEvent, addRecentlyUsedStyle, allStyles, refreshRecentImages, loadImageFromIndexedDb, setIsGenerating]);

  return { isGenerating: isGeneratingLocal, generationProgress, generateImage };
}
