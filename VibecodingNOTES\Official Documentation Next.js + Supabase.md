📚 Project Documentation & Reference Links

🚀 Next.js

Official Documentation: https://nextjs.org/docs

API Routes (Pages Router): https://nextjs.org/docs/api-routes/introduction

App Router & Layouts: https://nextjs.org/docs/app/building-your-application/routing

Authentication: https://nextjs.org/docs/app/building-your-application/authentication

Data Fetching: https://nextjs.org/docs/basic-features/data-fetching

Image Component: https://nextjs.org/docs/app/api-reference/components/image

Edge Functions & Middleware: https://nextjs.org/docs/app/building-your-application/edge-functions

Internationalization (i18n): https://nextjs.org/docs/app/building-your-application/i18n-routing

Deploying on Vercel: https://vercel.com/docs/platform/deployments

🗄️ Supabase

Official Documentation: https://supabase.com/docs

Getting Started Guide: https://supabase.com/docs/guides/getting-started

Auth Helpers: https://supabase.com/docs/guides/auth

Next.js Auth Integration: https://supabase.com/docs/guides/auth/auth-helpers/nextjs

JavaScript API Reference: https://supabase.com/docs/reference/javascript

Realtime (Subscriptions & Presence): https://supabase.com/docs/guides/realtime

Storage: https://supabase.com/docs/guides/storage

Database (Tables & SQL): https://supabase.com/docs/guides/database

Edge Functions: https://supabase.com/docs/guides/functions

Client Libraries: https://supabase.com/docs/reference/javascript/supabase-client

🗃️ Database & ORM

Prisma Documentation: https://www.prisma.io/docs/

Next.js with Prisma Guide: https://www.prisma.io/docs/guides/nextjs

⚙️ State Management & Data Fetching

SWR: https://swr.vercel.app/docs/getting-started

React Query (TanStack Query): https://tanstack.com/query/latest/docs/overview

Zustand: https://zustand-demo.pmnd.rs/

🎨 UI & Tooling

Tailwind CSS: https://tailwindcss.com/docs

NextAuth.js: https://next-auth.js.org/getting-started/introduction

ESLint & Prettier: https://nextjs.org/docs/basic-features/eslint

TypeScript in Next.js: https://nextjs.org/docs/basic-features/typescript

Sonner Toasts: https://sonner.emilkowal.ski/
Sonner Toasts Styling: https://sonner.emilkowal.ski/styling

ShadCN UI Components: https://ui.shadcn.com/docs/components