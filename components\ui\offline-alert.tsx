"use client";

import { useEffect, useRef, useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WifiOff, Wifi } from "lucide-react";

export function OfflineAlert() {
  const [isOnline, setIsOnline] = useState(typeof window !== 'undefined' ? navigator.onLine : true);
  const [showAlert, setShowAlert] = useState(false);
  const wasOffline = useRef(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline.current) {
        setShowAlert(true);
        setTimeout(() => setShowAlert(false), 3000);
        wasOffline.current = false;
      }
    };
    const handleOffline = () => {
      setIsOnline(false);
      setShowAlert(true);
      wasOffline.current = true;
    };
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    // On mount, if offline, show the alert
    if (!navigator.onLine) {
      setIsOnline(false);
      setShowAlert(true);
      wasOffline.current = true;
    }
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  if (!showAlert) return null;

  return (
    <div
      className="fixed left-1/2 top-6 z-50 w-full max-w-md -translate-x-1/2 px-4"
      role="status"
      aria-live="polite"
    >
      <Alert        className={`flex items-center gap-3 rounded-xl border-2 px-5 py-3 shadow-lg transition-colors duration-300
          ${isOnline
            ? "border-green-400/80 bg-green-600/90 text-green-50"
            : "border-red-400/80 bg-red-600/90 text-red-50"}
        `}
        style={{
          backdropFilter: "blur(6px)",
// at the top of your component file
import { useRef, useEffect } from "react";

export function OfflineAlert() {
  // … other hooks
+ const timeoutId = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleOnline = () => {
    setIsOnline(true);
    if (wasOffline.current) {
      setShowAlert(true);
-     setTimeout(() => setShowAlert(false), 3000);
+     timeoutId.current = setTimeout(() => setShowAlert(false), 3000);
      wasOffline.current = false;
    }
  };

  useEffect(() => {
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
+     if (timeoutId.current !== null) {
+       clearTimeout(timeoutId.current);
+     }
    };
  }, []);

  return (
    <>
      {isOnline ? (
        <Wifi className="size-5 min-w-5" />
      ) : (
        <WifiOff className="size-5 min-w-5" />
      )}
      <AlertDescription className="flex-1 text-center text-base font-medium">
        {/* … */}
      </AlertDescription>
    </>
  );
}      >
        {isOnline ? <Wifi className="size-5 min-w-5" /> : <WifiOff className="size-5 min-w-5" />}
        <AlertDescription className="flex-1 text-center text-base font-medium">
          {isOnline
            ? "Connection restored! You're back online."
            : "You're offline. Some features may not work properly."}
        </AlertDescription>
      </Alert>
    </div>
  );
}
