"use client";

import React, { createContext, useContext, useEffect, ReactNode, useRef, useState } from "react";
import useS<PERSON> from "swr";
import { createBrowserClient } from '@supabase/ssr';
import { User, Session } from "@supabase/supabase-js";
import { CacheService } from "../cache-service";
import { apiService } from "@/lib/api/apiService";
import { errorHandler } from "@/lib/utils/errorHandler";
import { clearRecentlyUsedStyles } from "./RecentlyUsedStylesContext";
import { useRouter, usePathname, useSearchParams } from "next/navigation";

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Fetcher for user favorites
const favoritesFetcher = async (url: string) => {
  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session?.user) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await apiService.get<any>(url, {
    Authorization: `Bearer ${session.access_token}`,
  });
  if (error) {
    errorHandler(error, { userMessage: "Failed to fetch favorites" });
    throw new Error(error);
  }
  return data;
};

// Context interface
interface UserContextType {
  user: User | null;
  isLoading: boolean;
  error: Error | null;
  favorites: string[];
  favoritesLoading: boolean;
  favoritesError: Error | null;
  refreshFavorites: () => void;
  signOut: () => Promise<void>;
}

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider component
export function UserProvider({ children, initialUser }: { children: ReactNode; initialUser?: User | null }) {
  const [user, setUser] = React.useState<User | null>(initialUser ?? null);
  const [isLoading, setIsLoading] = React.useState(initialUser === undefined);
  const [isInitialized, setIsInitialized] = React.useState(false);
  const prevUserIdRef = React.useRef<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // On mount, handle SSR hydration or no user
  React.useEffect(() => {
    if (initialUser !== undefined) {
      setUser(initialUser ?? null);
      prevUserIdRef.current = initialUser?.id ?? null;
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [initialUser]);

  // Fetch user favorites using SWR
  const {
    data: favoritesData,
    error: favoritesError,
    isLoading: favoritesLoading,
    mutate: refreshFavorites,
  } = useSWR(user ? `/api/user/favorites` : null, favoritesFetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 2 * 60 * 1000, // 2 minutes
    errorRetryCount: 2,
  });

  const favorites = favoritesData?.favorites || [];

  // Handle authentication state changes
  useEffect(() => {
    let mounted = true;

    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event: string, session: Session | null) => {
      if (event === "INITIAL_SESSION") return;
      await CacheService.clearCache();
      if (prevUserIdRef.current) {
        await clearRecentlyUsedStyles(prevUserIdRef.current);
      }
      if (mounted) {
        setUser(session?.user ?? null);
        prevUserIdRef.current = session?.user?.id ?? null;
        setIsLoading(false);
        setIsInitialized(true);
      }
    });

    // Always check session on mount (client-side)
    supabase.auth.getSession().then(({ data: { session } }: { data: { session: Session | null } }) => {
      if (mounted) {
        setUser(session?.user ?? null);
        prevUserIdRef.current = session?.user?.id ?? null;
        setIsLoading(false);
        setIsInitialized(true);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [initialUser]);

  const signOut = async () => {
    await CacheService.clearCache();
    if (prevUserIdRef.current) {
      await clearRecentlyUsedStyles(prevUserIdRef.current);
      // Also clear upload cache for previous user if needed
      // (call a clearUploadCache function if implemented)
    }
    await supabase.auth.signOut();
    if (typeof window !== "undefined") {
      window.sessionStorage.setItem("showSignoutToast", "1");
    }
    window.location.href = "/login?signout=1";
  };

  const value: UserContextType = {
    user,
    isLoading: isLoading || !isInitialized,
    error: null,
    favorites,
    favoritesLoading,
    favoritesError,
    refreshFavorites,
    signOut,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

// Hook to use the user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}

// Hook for checking if a style is favorited
export function useIsFavorite(styleId: string) {
  const { favorites, favoritesLoading } = useUser();
  return {
    isFavorite: favorites.includes(styleId),
    isLoading: favoritesLoading,
  };
}
