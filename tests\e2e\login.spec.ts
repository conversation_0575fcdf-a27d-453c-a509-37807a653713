import { test, expect } from "@playwright/test";

test.describe("Login Page", () => {
  test("should render login form", async ({ page }) => {
    await page.goto("/login");
    await expect(page.getByLabel("Email")).toBeVisible();
    await expect(page.getByLabel("Password")).toBeVisible();
    await expect(
      page.getByRole("button", { name: /Sign in|Sign up/i }),
    ).toBeVisible();
  });

  test("should have correct SEO meta tags", async ({ page }) => {
    await page.goto("/login");
    await expect(page).toHaveTitle("PxlMorph AI - AI-Powered Image Generation");
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      "content",
      /Create stunning AI-generated images with advanced style controls and real-time editing capabilities\./i
    );
  });
});
