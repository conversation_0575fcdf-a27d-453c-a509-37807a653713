-- Update update_like_count function to set search_path for security
CREATE OR REPLACE FUNCTION public.update_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF (TG_OP = 'INSERT') THEN
        UPDATE public.images
        SET like_count = like_count + 1
        WHERE id = NEW.image_id;
    ELSIF (TG_OP = 'DELETE') THEN
        UPDATE public.images
        SET like_count = like_count - 1
        WHERE id = OLD.image_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public; 