import { useCallback, useState } from "react";
import { apiService } from "@/lib/api/apiService";
import { errorHandler } from "@/lib/utils/errorHandler";
import { showDeleteToast } from "@/lib/utils/toast";
import { createBrowserClient } from '@supabase/ssr';
import { useAllUserImages } from "@/lib/hooks/useAllUserImages";
import { useRecentCreations } from "@/lib/hooks/useRecentCreations";
import { useCommunityImages } from "@/lib/hooks/useCommunityImages";
import { useUser } from "@/lib/contexts/UserContext";
import { GeneratedImage } from "@/lib/types";
import { toast } from "sonner";

/**
 * useImageDelete - Hook for deleting images with API, toast feedback, and optimistic UI updates.
 * @returns {Object} { deleteImage, isDeleting }
 */
export function useImageDelete() {
  const [isDeleting, setIsDeleting] = useState(false);
  const { user } = useUser();
  const { mutate: mutateAllUserImages } = useAllUserImages(user?.id);
  const { mutate: mutateRecentCreations } = useRecentCreations(user?.id);
  const { mutate: mutateCommunityRecent } = useCommunityImages("recent");
  const { mutate: mutateCommunityTrending } = useCommunityImages("trending");

  const deleteImage = useCallback(
    async (imageId: string, image: GeneratedImage) => {
      if (isDeleting) return { success: false, error: "Deletion already in progress." };
      setIsDeleting(true);
      
      const optimisticUpdate = (cacheKey: any, message: string) => {
        const { mutate } = require("swr");
        mutate(
          cacheKey,
          (currentImages: GeneratedImage[] = []) => currentImages.filter((img) => img.id !== imageId),
          false
        );
      };

      const rollback = (cacheKey: any) => {
        const { mutate } = require("swr");
        mutate(
          cacheKey,
          (currentImages: GeneratedImage[] = []) => [image, ...currentImages],
          false
        );
      };
      
      // Optimistically update all relevant SWR caches
      if (user?.id) {
        optimisticUpdate(["all-user-images", user.id], "all");
        optimisticUpdate(["recent-creations", user.id], "recent");
      }
      optimisticUpdate(["community-images", "recent", 20, 0], "community-recent");
      optimisticUpdate(["community-images", "trending", 20, 0], "community-trending");

      try {
        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) throw new Error("Authentication required");

        const { data, error } = await apiService.request(
          "/api/delete-image",
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            },
            body: JSON.stringify({ imageId }),
          }
        );
        
        if (error) throw new Error(error);

        showDeleteToast(true);
        // Revalidate caches after successful API call
        if (user?.id) {
          mutateAllUserImages();
          mutateRecentCreations();
        }
        mutateCommunityRecent();
        mutateCommunityTrending();

        // Dispatch custom event for immediate UI updates
        if (typeof window !== "undefined") {
          window.dispatchEvent(
            new CustomEvent("imageDeleted", {
              detail: { imageId, userId: user?.id },
            }),
          );
        }

        return { success: true };
      } catch (err) {
        // Rollback optimistic updates on error
        if (user?.id) {
          rollback(["all-user-images", user.id]);
          rollback(["recent-creations", user.id]);
        }
        rollback(["community-images", "recent", 20, 0]);
        rollback(["community-images", "trending", 20, 0]);
        
        errorHandler(err, { userMessage: "Failed to delete image." });
        return { success: false, error: (err as Error).message };
      } finally {
        setIsDeleting(false);
      }
    }, [isDeleting, user?.id, mutateAllUserImages, mutateRecentCreations, mutateCommunityRecent, mutateCommunityTrending]
  );

  return { deleteImage, isDeleting };
}
