"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
} from "react";

// Define the shape of a recently used style
export interface RecentlyUsedStyle {
  id: string;
  name: string;
  example_image: string;
  createdAt: string;
}

const DB_NAME = "PxlMorph_RecentlyUsedStylesDB";
const STORE_NAME = "recentlyUsedStylesStore";
const KEY_PREFIX = "recentlyUsedStyles_";
const MAX = 5;

let dbPromise: Promise<IDBDatabase> | null = null;

function openDb(): Promise<IDBDatabase> {
  if (dbPromise) return dbPromise;
  dbPromise = new Promise((resolve, _reject) => {
    const request = indexedDB.open(DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => _reject(request.error);
  });
  return dbPromise;
}

async function getRecentlyUsedStyles(userId?: string, retry = 0): Promise<RecentlyUsedStyle[]> {
  if (!userId) return [];
  try {
    const db = await openDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(STORE_NAME, "readonly");
      const store = tx.objectStore(STORE_NAME);
      const req = store.get(KEY_PREFIX + userId);
      req.onsuccess = () => {
        let arr = req.result ?? [];
        arr = Array.isArray(arr)
          ? arr.filter(
              (s) =>
                s &&
                s.id &&
                s.name &&
                s.example_image &&
                typeof s.createdAt === "string",
            )
          : [];
        resolve(arr);
      };
      req.onerror = (_e) => {
        // If the DB is closing, retry once after a short delay
        if (req.error && req.error.name === "InvalidStateError" && retry < 2) {
          setTimeout(() => {
            getRecentlyUsedStyles(userId, retry + 1).then(resolve);
          }, 100);
        } else {
          console.warn("[RecentlyUsed] IndexedDB get error:", req.error);
          resolve([]);
        }
      };
    });
  } catch (err: any) {
    if (err?.name === "InvalidStateError" && retry < 2) {
      // Retry once if DB is closing
      await new Promise(res => setTimeout(res, 100));
      return getRecentlyUsedStyles(userId, retry + 1);
    }
    console.warn("[RecentlyUsed] getRecentlyUsedStyles failed:", err);
    return [];
  }
}

async function setRecentlyUsedStyles(userId: string, list: RecentlyUsedStyle[]) {
  if (!userId) return;
  try {
    const db = await openDb();
    return new Promise<void>((resolve, _reject) => {
      const tx = db.transaction(STORE_NAME, "readwrite");
      const store = tx.objectStore(STORE_NAME);
      const req = store.put(list, KEY_PREFIX + userId);
      req.onsuccess = () => resolve();
      req.onerror = (e) => {
        console.warn("[RecentlyUsed] IndexedDB put error:", req.error, e);
        resolve(); // Don't throw, just log
      };
    });
  } catch (err) {
    console.warn("[RecentlyUsed] setRecentlyUsedStyles failed:", err);
    // Don't throw
  }
}

async function clearRecentlyUsedStyles(userId?: string) {
  if (!userId) return;
  try {
    const db = await openDb();
    return new Promise<void>((resolve, _reject) => {
      const tx = db.transaction(STORE_NAME, "readwrite");
      const store = tx.objectStore(STORE_NAME);
      const req = store.delete(KEY_PREFIX + userId);
      req.onsuccess = () => resolve();
      req.onerror = (e) => {
        console.warn("[RecentlyUsed] IndexedDB delete error:", req.error, e);
        resolve(); // Don't throw, just log
      };
    });
  } catch (err) {
    console.warn("[RecentlyUsed] clearRecentlyUsedStyles failed:", err);
    // Don't throw
  }
}

interface RecentlyUsedStylesContextType {
  styles: RecentlyUsedStyle[];
  addStyle: (styleObj: RecentlyUsedStyle) => Promise<void>;
  clear: () => Promise<void>;
  loaded: boolean;
}

const RecentlyUsedStylesContext = createContext<
  RecentlyUsedStylesContextType | undefined
>(undefined);

export function RecentlyUsedStylesProvider({
  children,
  userId,
}: {
  children: ReactNode;
  userId?: string;
}) {
  const [styles, setStyles] = useState<RecentlyUsedStyle[]>([]);
  const [loaded, setLoaded] = useState(false);

  // Load from IndexedDB on mount or user change
  useEffect(() => {
    if (!userId) {
      setStyles([]);
      setLoaded(true);
      return;
    }
    getRecentlyUsedStyles(userId)
      .then((list) => {
        setStyles(Array.isArray(list) ? list : []);
        setLoaded(true);
      })
      .catch((err) => {
        console.warn("[RecentlyUsed] Provider failed to load styles:", err);
        setStyles([]);
        setLoaded(true);
      });
  }, [userId]);

  // Add style after generation
  const addStyle = useCallback(async (styleObj: RecentlyUsedStyle) => {
    if (!userId) return;
    try {
      // Atomic update: read → update → write
      const prev = await getRecentlyUsedStyles(userId);
      // Remove any existing by id
      const filtered = prev.filter((s) => s.id !== styleObj.id);
      // Insert fresh at top
      const next = [styleObj, ...filtered].slice(0, MAX);
      await setRecentlyUsedStyles(userId, next);
      setStyles(next);
    } catch (err) {
      console.error("[RecentlyUsed] addStyle failed:", err);
    }
  }, [userId]);

  // Clear for testing/dev or on logout
  const clear = useCallback(async () => {
    if (!userId) return;
    await clearRecentlyUsedStyles(userId);
    setStyles([]);
  }, [userId]);

  const value: RecentlyUsedStylesContextType = {
    styles,
    addStyle,
    clear,
    loaded,
  };

  return (
    <RecentlyUsedStylesContext.Provider value={value}>
      {children}
    </RecentlyUsedStylesContext.Provider>
  );
}

export function useRecentlyUsedStyles() {
  const ctx = useContext(RecentlyUsedStylesContext);
  if (!ctx)
    throw new Error(
      "useRecentlyUsedStyles must be used within RecentlyUsedStylesProvider",
    );
  return ctx;
}

export { clearRecentlyUsedStyles };
