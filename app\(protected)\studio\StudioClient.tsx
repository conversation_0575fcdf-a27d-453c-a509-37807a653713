"use client";
import { Suspense } from "react";
import UnifiedLoader from "@/components/ui/unified-loader";
import { StudioHero } from "./StudioHero";
import { useStudioLogic } from "./useStudioLogic";
import { RecentlyUsedStylesProvider } from "@/lib/contexts/RecentlyUsedStylesContext";
import { useUser } from "@/lib/contexts/UserContext";
import { createBrowserClient } from '@supabase/ssr';
import React from "react";
import { useGeneration } from "@/lib/contexts/GenerationContext";

export default function StudioClient() {
  const { user, isLoading: userLoading } = useUser();
  const [clientSessionUserId, setClientSessionUserId] = React.useState<string | null>(null);
  React.useEffect(() => {
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    supabase.auth.getSession().then(({ data: { session } }) => {
      setClientSessionUserId(session?.user?.id ?? null);
    });
  }, []);
  return (
    <>
      {userLoading ? (
        <div className="flex min-h-screen items-center justify-center">
          <UnifiedLoader text="Loading Studio..." />
        </div>
      ) : !user ? null : (
        <RecentlyUsedStylesProvider userId={user?.id}>
          <StudioClientInner />
        </RecentlyUsedStylesProvider>
      )}
    </>
  );
}

function StudioClientInner() {
  const { user } = useUser();
   
  const {
    isLoading,
    isApiConfigured,
    clientIsOnline,
    generationProgress,
    selectedStyle,
    uploadedImage,
    customPrompt,
    recentImages,
    hasLoadedRecentImages,
    setSelectedStyle,
    setUploadedImage,
    setCustomPrompt,
    handleGenerateImage,
    handleImageShare,
    handleImageDelete,
    checkAuth,
    justGeneratedImageId,
  } = useStudioLogic();
  const { isGenerating, setIsGenerating } = useGeneration();

  // Wrap the generate handler to set global state
  const onGenerate = async () => {
    setIsGenerating(true);
    try {
      await handleGenerateImage();
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <>
      <Suspense
        fallback={
          <div className="flex min-h-screen items-center justify-center">
            <UnifiedLoader />
          </div>
        }
      >
        <StudioHero />
        {/* Removed all props, as StudioHero does not accept any */}
        {/* If you need to render the rest of the studio UI, add it below */}
      </Suspense>
    </>
  );
} 