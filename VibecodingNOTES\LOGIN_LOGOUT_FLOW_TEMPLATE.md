# Login/Logout Flow Implementation Template (Next.js + Supabase + SSR)

This template provides a comprehensive, technically detailed guide for implementing a robust login/logout flow in a modern fullstack web application using **Next.js (App Router)**, **Supabase authentication**, and **Server-Side Rendering (SSR)**. It is designed to be adaptable to any Next.js + Supabase project and ensures best practices for security, user experience, and maintainability.

---

## 1. Authentication State Management

### Client-Side (Next.js App Router)
- **Context Provider**: Use a React context provider to manage and propagate the authenticated user state (`user`), loading state (`isLoading`), and error state throughout the app. Place this provider at the top level of your layout for protected routes.
- **Session Hydration**: On mount, hydrate the user state from the current session using Supabase's `getSession()` method (from the browser client). This ensures the client always has the latest session info after SSR hydration.
- **Auth State Listener**: Subscribe to Supabase's `onAuthStateChange` to react to login/logout/session refresh events. On any change, update the user state, clear sensitive caches (memory, SWR, IndexedDB), and trigger any necessary side effects (e.g., clearing user-specific data).
- **Sign Out Method**: Provide a `signOut` function that clears caches, calls `supabase.auth.signOut()`, and redirects to the login page with a signout indicator (e.g., `?signout=1`).

### Server-Side (SSR/SSG)
- **Supabase Auth Helpers**: Use Supabase's `createServerClient` (from `@supabase/ssr`) to extract the session from cookies in server components, layouts, and API routes. This ensures SSR/SSG pages have access to the authenticated user.
- **User Injection**: Inject the authenticated user into the initial render context (e.g., via a provider or props) for SSR/SSG. Pass this user as the initial value to your client-side context provider.
- **Redirect on Failure**: If no valid session is found on the server, redirect to the login page with a session-expired indicator (e.g., `?reason=session-expired`).

---

## 2. Session Persistence
- **Cookie-Based**: Supabase manages session tokens via secure, HTTP-only cookies. Ensure cookies are set with appropriate domain, path, and SameSite attributes (handled by Supabase helpers).
- **Auto-Refresh**: Implement logic to refresh the session/token before it expires. Use Supabase's `refreshSession()` if the token is close to expiry (e.g., <5 minutes left).
- **Session Check on Load**: Always check for a valid session on app load and after any auth state change, both client and server.

---

## 3. Protected Route Enforcement
- **Next.js Middleware**: Use Next.js middleware to check for the presence of a valid Supabase session cookie on all protected routes. If missing or invalid, redirect to login with a reason (e.g., `?reason=session-expired`).
- **Client-Side Guard**: Wrap protected page components in a guard that checks the user context. If loading, show a loader; if unauthenticated, render nothing (middleware will have redirected).
- **SSR/SSG Enforcement**: On server-rendered pages, check the session before rendering. If invalid, redirect early to avoid leaking protected content.

---

## 4. Login/Logout User Experience & UI Actions

A smooth, modern UX/UI is critical for authentication flows. The following best practices and patterns ensure a seamless experience:

- **Loading States**: Always show a loading spinner or skeleton UI while authentication state is being determined (e.g., after page load, during login, or after logout). Use overlays or fade animations to indicate transitions.
- **Toasts & Feedback**: Use toast notifications for all major auth actions:
  - Show a welcome toast after successful login (persisted via `sessionStorage` if needed).
  - Show a signout toast after logout (persisted via `sessionStorage` if needed).
  - Show a session-expired toast if the session is invalid or expired, with a clear message and icon.
  - Show error toasts for failed login/signup attempts, with actionable messages.
- **Redirects**: Automatically redirect users after successful login (e.g., to `/studio` or dashboard) and after logout (to `/login?signout=1`).
- **Form UX**:
  - Use tabbed or toggled forms for login/signup.
  - Disable submit buttons and show a spinner while processing.
  - Show inline error messages for invalid credentials or validation errors.
  - Show a success message after signup (e.g., "Check your email to confirm your account").
- **Session Expired Handling**:
  - If a session expires, redirect to login with a `?reason=session-expired` query and show a toast explaining the situation.
  - Optionally, fade out the current UI and show a loader during the redirect.
- **Animation & Transitions**:
  - Use fade-in/fade-out or slide transitions for overlays, loaders, and form state changes.
  - Animate the transition between login, signup, and loading states for a polished feel.
- **Accessibility**:
  - Ensure all forms and toasts are accessible via keyboard and screen readers.
  - Use ARIA roles and labels for all interactive elements.
- **Multiple Tabs/Windows**:
  - Ensure logout/login is synchronized across tabs by listening to Supabase auth state changes or storage events.
- **Consistent Layout**:
  - Use a unified layout for all auth-related pages, with consistent padding, centering, and background.

---

## 5. Analytics & Event Tracking
- **Login/Signup Events**: Track login and signup events with method and timestamp (e.g., using Firebase Analytics or another provider).
- **Logout Events**: Track logout events with timestamp.
- **Error Events**: Track authentication errors with type, message, and context.
- **Session Expiry**: Track session expiration events for monitoring.
- **User Properties**: Optionally set user ID and properties in analytics after login.

---

## 6. Session Expiration & Edge Cases
- **Session Expiry Handling**: Detect expired or invalid sessions on both client and server. Redirect to login with a session-expired reason and show a toast explaining the situation.
- **Token Refresh**: Proactively refresh tokens before expiry. If refresh fails, treat as session expired and redirect.
- **Multiple Tabs/Windows**: Listen for Supabase auth state changes and synchronize logout across tabs (e.g., via storage events or SDK listeners).
- **SSR/CSR Consistency**: Ensure user state is consistent between server and client renders by passing the initial user from SSR to the client context.
- **Cache Invalidation**: On logout or user change, clear all user-specific caches (memory, IndexedDB, SWR, etc.).
- **API Security**: For API routes, always validate the Supabase JWT from the Authorization header and check user permissions on every request.

---

## 7. Implementation Checklist
- [ ] Context provider for user/auth state (client)
- [ ] Supabase SSR helpers for session extraction (server)
- [ ] Auth state listener and session hydration (client)
- [ ] Secure session persistence (Supabase cookies)
- [ ] Next.js middleware for protected route enforcement
- [ ] Client-side guard for protected components
- [ ] Login/signup/logout forms with feedback and Supabase methods
- [ ] Analytics for all auth events
- [ ] Session expiration detection and handling (client/server)
- [ ] Cache invalidation on logout
- [ ] Consistent SSR/CSR user state
- [ ] API route user validation and permission checks

---

**Best Practices:**
- Always use Supabase's official SSR helpers for session management in Next.js App Router.
- Never expose protected content on the server if the session is invalid.
- Use Next.js middleware for fast, edge-enforced route protection.
- Keep user state in sync between SSR and CSR by passing the initial user from the server to the client context.
- Invalidate all caches and sensitive data on logout or user change.
- Track all auth events for analytics and monitoring.
- Use toasts and clear feedback for all auth-related user actions.
- Handle session expiration gracefully and securely.
- Prioritize smooth, accessible, and visually consistent UI/UX for all auth flows.

---

**Result:**
Following this template ensures a secure, user-friendly, and analytics-ready login/logout flow for any Next.js + Supabase project, robust to edge cases and easy to maintain or extend. 