# PxlMorph AI

A modern AI-powered image generation application built with Next.js, Supabase, and OpenAI's DALL-E 3.

## Features

- 🎨 AI-powered image generation with DALL-E 3
- 🔐 Secure user authentication with Supabase
- 💾 Permanent image storage with proper RLS policies
- 🌐 Community sharing features
- 📱 Responsive design with beautiful UI
- ⚡ Real-time updates and caching

## Environment Variables Required

To run this application, you need to set up the following environment variables in your `.env.local` file:

### Required Environment Variables

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
```

### How to Get These Keys

1. **Supabase Keys**:
   - Go to your [Supabase Dashboard](https://supabase.com/dashboard)
   - Select your project
   - Go to Settings → API
   - Copy the `URL` for `NEXT_PUBLIC_SUPABASE_URL`
   - Copy the `anon public` key for `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - Copy the `service_role` key for `SUPABASE_SERVICE_ROLE_KEY`

2. **OpenAI API Key**:
   - Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy it for `OPENAI_API_KEY`

### Supabase Setup

1. **Database Schema**: Run the migration file in `supabase/migrations/` to create the required tables
2. **Storage Bucket**: Create a bucket named `private-images` in your Supabase Storage
3. **RLS Policies**: Ensure proper Row Level Security policies are configured for both database tables and storage

### Storage Configuration

The application uses Supabase Storage with the following setup:

- **Bucket Name**: `private-images`
- **Bucket Type**: Private (with RLS policies)
- **File Structure**: `{user_id}/{timestamp}-{style}.png`

### Required RLS Policies

**For Storage (`private-images` bucket):**

- Users can upload images to their own folder
- Users can view their own images
- Users can view shared images
- Users can delete their own images

**For Database Tables:**

- Users can read/write their own data
- Shared images are publicly readable
- Proper foreign key constraints

### Setup Instructions

1. Clone the repository
2. Install dependencies: `npm install`
3. Create a `.env.local` file in the project root
4. Add all the required environment variables
5. Set up your Supabase database and storage
6. Run the development server: `npm run dev`

### Security Features

- **Server-side image processing**: Images are downloaded and processed on the server to avoid CORS issues
- **Proper authentication**: All API routes verify user sessions
- **RLS enforcement**: Database and storage operations respect Row Level Security policies
- **Secure token handling**: JWT tokens are properly validated on the server

### Testing

Run the comprehensive test suite:

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:openai      # OpenAI integration tests
npm run test:supabase    # Supabase integration tests
npm run test:routes      # Next.js route tests
npm run test:integration # Full integration tests
```

**Important**:

- Never commit your `.env.local` file to version control
- Use the service role key only on the server side
- Ensure your Supabase RLS policies are properly configured
- Test your setup with the provided test suite

## Architecture

- **Frontend**: Next.js 13+ with App Router
- **Backend**: Next.js API routes with server-side processing
- **Database**: Supabase PostgreSQL with RLS
- **Storage**: Supabase Storage with private buckets
- **Authentication**: Supabase Auth with JWT tokens
- **AI**: OpenAI DALL-E 3 for image generation (and gpt-image-1 for edits)

## API Endpoints

### `/api/generate-image` (POST)

Handles image generation based on predefined styles and an optional inspiration image.

- **Authentication**: Bearer Token (Supabase JWT)
- **Request Body**: `multipart/form-data`
  - `styleId` (string, required): ID of the style to apply.
  - `customPrompt` (string, optional): Additional user prompt (currently ignored by the service in favor of style-based prompt).
  - `inspirationImage` (File, required): Image file to be used as inspiration.
- **Success Response (200)**:
  ```json
  {
    "imageData": {
      "id": "img_uuid",
      "user_id": "user_uuid",
      "image_url": "user_uuid/generated/timestamp-style.png", // Path in Supabase storage
      "style": "style_id",
      "prompt": "Reimagine this picture in [style name] style"
      // ... other fields
    },
    "message": "Image generated and saved successfully"
  }
  ```
- **Error Responses**: Standard HTTP error codes (400, 401, 500) with JSON body: `{"error": "Error message"}`.

### `/api/edit-image` (POST)

Handles image editing based on a user-provided prompt and an input image, using the `gpt-image-1` model.

- **Authentication**: Bearer Token (Supabase JWT)
- **Request Body**: `multipart/form-data`
  - `prompt` (string, required): Textual prompt describing the desired edit. Max 4000 characters.
  - `image` (File, required): The image file to be edited. Must be PNG, JPEG, GIF, or WEBP, and under 4MB.
- **Success Response (200)**:
  ```json
  {
    "message": "Image edited and saved successfully.",
    "imageData": {
      "id": "img_uuid",
      "userId": "user_uuid",
      "storagePath": "user_uuid/edits/generated_uuid.png", // Path in Supabase storage
      "prompt": "User provided prompt",
      "style": "edit", // Indicates an edited image
      "createdAt": "timestamp"
    }
  }
  ```
- **Example cURL Request**:
  ```bash
  curl -X POST \
    -H "Authorization: Bearer YOUR_SUPABASE_JWT" \
    -F "prompt=Make the cat wear a party hat" \
    -F "image=@/path/to/your/cat_image.png" \
    http://localhost:3000/api/edit-image
  ```
- **Error Responses**:
  - `400 Bad Request`: Invalid input (e.g., missing prompt/image, wrong file type/size, content policy violation).
    ```json
    {
      "error": "Invalid request data.",
      "details": { "prompt": ["Prompt is required"] }
    }
    ```
    ```json
    {
      "error": "Request violates OpenAI content policy. Please modify your prompt or image."
    }
    ```
  - `401 Unauthorized`: Missing or invalid authentication token.
  - `429 Too Many Requests`: Rate limit exceeded.
  - `500 Internal Server Error`: Server-side issues (e.g., OpenAI API down, Supabase error).

## Production Deployment

Before deploying to production:

1. Ensure all environment variables are set
2. Run the test suite to verify configuration
3. Set up proper monitoring and logging
4. Configure your domain and SSL certificates
5. Review and test your RLS policies thoroughly

## Production Checklist for Delete Flow

- Ensure the following environment variables are set in production:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `SUPABASE_SERVICE_ROLE_KEY`
- User must be authenticated (session present) to delete images.
- All delete requests must include a valid imageId in the JSON body.
- If deletion fails, check server logs for missing config, session, or malformed requests.
- Run `next build` and `next start` locally to catch SSR/env/config issues before deploying.

## Updating JSON or Static Files (Server-Side Imports)

Whenever you update a JSON or static file that is imported server-side, always:

1. **Stop the dev server.**
2. **Delete the `.next` directory**
   - On Mac/Linux: `rm -rf .next`
   - On Windows PowerShell: `Remove-Item -Recurse -Force .next`
3. **Restart the dev server.**

This ensures your app always uses the latest data from your static files.

## 🏗️ Architecture & Best Practices

### Centralized Business Logic
- All major business logic (like, share, delete, download, generate) is encapsulated in shared React hooks in `lib/hooks/`.
- Formatting, error handling, and toast feedback are centralized in `lib/utils/`.

### Shared Hooks
- `useImageDownload` — Handles image download logic, including auth, formatting, and toasts.
- `useLikeToggle` — Handles like/unlike logic with optimistic UI, API, and toasts.
- `useImageShare` — Handles sharing/copying links with native share, clipboard, and toasts.
- `useImageDelete` — Handles image deletion with confirmation, API, and toasts.
- `useImageGeneration` — Handles image generation, validation, API, analytics, and toasts.

### Formatting & Feedback
- All date, style, and file name formatting is in `lib/utils/format.ts`.
- All toast logic is in `lib/utils/toast.ts`.
- All error handling is in `lib/utils/errorHandler.ts`.

### Testing
- All hooks/utilities should have robust unit tests.
- Integration tests cover key user flows (image generation, like/unlike, share, delete).

### Contribution Guidelines
- Always use shared hooks/utilities for new business logic or formatting.
- Add JSDoc comments to new hooks/utilities.
- Keep user feedback (toasts, errors) consistent and centralized.

---

This architecture ensures the codebase is DRY, maintainable, and easy to extend. For questions or contributions, see the code comments and this section for guidance.
