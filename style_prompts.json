{"Anime & Cartoon": [{"id": "studio-<PERSON><PERSON><PERSON><PERSON>", "title": "Studio Ghibli", "prompt": "Transform this image into the Studio Ghibli style. Preserve the original composition and key forms. Render it with soft lighting, warm hand-painted textures, and whimsical background details, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/studio-ghibli.jpg"}, {"id": "pixar-3d", "title": "Pixar 3D", "prompt": "Transform this image into the Pixar/Disney 3D style. Preserve the original composition and key forms. Render it with smooth 3D shading, expressive facial features, and polished surfaces, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/pixar-3d.jpg"}, {"id": "the-simpsons", "title": "The Simpsons", "prompt": "Transform this image into The Simpsons style. Preserve the original composition and key forms. Render it with simple outlines, flat bright colors, and yellow skin tones, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/the-simpsons.webp"}, {"id": "south-park", "title": "South Park", "prompt": "Transform this image into the South Park paper cutout style. Preserve the original composition and key forms. Render it with flat shapes, rough edges, and simple color fills, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/south-park.webp"}, {"id": "modelling-clay", "title": "<PERSON>", "prompt": "Transform this image into a modeling clay stop-motion animation style. Preserve the original composition and key forms. Render it with soft, rounded shapes, visible sculpting marks, and playful character proportions, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/modeling-clay.jpg"}, {"id": "burtonesque", "title": "<PERSON>", "prompt": "Transform this image into a gothic-inspired stop-motion animation style reminiscent of classic dark fantasy films. Preserve the original composition and key forms. Render it with pale skin, large expressive eyes, elongated limbs, dramatic lighting, and whimsical gothic backgrounds, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/burtonesque.jpg"}, {"id": "pokemon", "title": "Pokémon", "prompt": "Transform this image into Pokémon anime style. Preserve the original composition and key forms. Render it with crisp linework, bright color flats, large sparkling eyes, and simplified forms, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/pokemon.jpg"}, {"id": "lo-fi-anime", "title": "Lo-fi Anime", "prompt": "Transform this image into lo-fi anime style. Preserve the original composition and key forms. Render it with grainy textures, muted pastel colors, and warm soft lighting, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/lo-fi-anime.jpg"}, {"id": "shojo-manga", "title": "<PERSON><PERSON><PERSON>", "prompt": "Transform this image into shojo manga style. Preserve the original composition and key forms. Render it with large expressive eyes, sparkle overlays, fine linework, and soft blush tones, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/shojo-manga.jpg"}, {"id": "rick-and-morty", "title": "<PERSON> and <PERSON><PERSON><PERSON>", "prompt": "Transform this image into <PERSON> and <PERSON><PERSON><PERSON> style. Preserve the original composition and key forms. Render it with bold black outlines, exaggerated facial expressions, and flat colors, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/rick-and-morty.jpg"}, {"id": "adventure-time", "title": "Adventure Time", "prompt": "Transform this image into the Adventure Time style. Preserve the original composition and key forms. Render it with simplistic shapes, bright pastel colors, and quirky character designs, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/adventure-time.jpg"}, {"id": "steven-universe", "title": "<PERSON>", "prompt": "Transform this image into Steven Universe style. Preserve the original composition and key forms. Render it with smooth gradients, pastel colors, and simplified geometric shapes, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/steven-universe.jpg"}, {"id": "anime-cyberpunk", "title": "Anime Cyberpunk", "prompt": "Transform this image into anime cyberpunk style. Preserve the original composition and key forms. Render it with neon lights, high contrast, and futuristic elements, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/anime-cyberpunk.jpg"}, {"id": "kawaii-chibi", "title": "<PERSON><PERSON>", "prompt": "Transform this image into kawaii chibi style. Preserve the original composition and key forms. Render it with oversized heads, small bodies, and cute pastel colors, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/kawaii-chibi.jpg"}], "Movie, Comic & Poster": [{"id": "marvel-comic", "title": "Marvel Comic", "prompt": "Transform this image into a Marvel comic book style. Preserve the original composition and key forms. Render it with bold outlines, dynamic perspectives, halftone shading, and dramatic action lines, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/marvel-comic.jpg"}, {"id": "stranger-things", "title": "Stranger Things", "prompt": "Transform this image into a Stranger Things poster style. Preserve the original composition and key forms. Render it with grainy textures, retro typography, and atmospheric glow effects, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/stranger-things.jpg"}, {"id": "wes-anderson", "title": "<PERSON>", "prompt": "Transform this image into a Wes Anderson style. Preserve the original composition and key forms. Render it with symmetrical framing, pastel color blocks, and flat graphic shapes, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/wes-anderson.jpg"}, {"id": "pop-art", "title": "Pop Art", "prompt": "Transform this image into Pop Art in the style of Warhol. Preserve the original composition and key forms. Render it with bright duotone screenprints, bold outlines, and repeated motif panels, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/pop-art.jpg"}, {"id": "noir-film", "title": "Noir Film", "prompt": "Transform this image into a classic noir film style. Preserve the original composition and key forms. Render it with high contrast black and white, atmospheric lighting, and cinematic composition, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/noir-film.jpg"}, {"id": "pixar-concept-art", "title": "Pixar Concept Art", "prompt": "Transform this image into Pixar concept art style. Preserve the original composition and key forms. Render it with painterly brush strokes, vibrant colors, and soft lighting, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/pixar-concept-art.jpg"}, {"id": "disney-classic", "title": "Disney Classic", "prompt": "Transform this image into classic Disney Princess style. Preserve the original composition and key forms. Render it with delicate linework, warm colors, and elegant expressions, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/disney-classic.jpg"}], "Game-Inspired": [{"id": "minecraft", "title": "Minecraft", "prompt": "Transform this image into Minecraft voxel style. Preserve the original composition and key forms. Render it with large blocky geometry, oversized cubic proportions, and simplified pixelated textures, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/minecraft.jpg"}, {"id": "zelda-breath-of-the-wild", "title": "<PERSON><PERSON><PERSON>", "prompt": "Transform this image into the Zelda: Breath of the Wild style. Preserve the original composition and key forms. Render it with watercolor-like washes, cel-shading outlines, and soft atmospheric lighting, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/zelda-breath-of-the-wild.jpg"}, {"id": "gta", "title": "GTA", "prompt": "Transform this image into a GTA loading screen style. Preserve the original composition and key forms. Render it with bold vector portraits, saturated flat colors, and dramatic shading, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/gta.jpg"}, {"id": "fortnite", "title": "Fortnite", "prompt": "Transform this image into the Fortnite style. Preserve the original composition and key forms. Render it with vibrant color palettes, stylized 3D proportions, and crisp outlines, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/fortnite.jpg"}, {"id": "overwatch", "title": "Overwatch", "prompt": "Transform this image into the Overwatch style. Preserve the original composition and key forms. Render it with dynamic lighting, sci-fi color accents, and stylized 3D realism, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/overwatch.jpg"}, {"id": "among-us", "title": "Among Us", "prompt": "Transform this image into Among Us cartoon style. Preserve the original composition and key forms. Render it with simple shapes, bright colors, and minimalistic detail, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/among-us.jpg"}, {"id": "cuphead", "title": "Cuphead", "prompt": "Transform this image into Cuphead vintage cartoon style. Preserve the original composition and key forms. Render it with 1930s rubber hose animation style, grainy textures, and muted sepia tones, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/cuphead.jpg"}, {"id": "the-legend-of-zelda-8-bit", "title": "Zelda 8-bit", "prompt": "Transform this image into 8-bit Legend of Zelda style. Preserve the original composition and key forms. Render it with pixel art, limited color palette, and retro game resolution style, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/the-legend-of-zelda-8-bit.jpg"}, {"id": "animal-crossing", "title": "Animal Crossing", "prompt": "Transform this image into Animal Crossing style. Preserve the original composition and key forms. Render it with soft pastel colors, simple shapes, and cozy, cheerful atmosphere, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/animal-crossing.jpg"}], "Art-History": [{"id": "van-gogh", "title": "<PERSON>", "prompt": "Transform this image into the <PERSON> style. Preserve the original composition and key forms. Render it with swirling impasto brushstrokes and bold complementary colors, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/van-gogh.jpg"}, {"id": "mucha", "title": "<PERSON><PERSON>", "prompt": "Transform this image into the Art Nouveau style inspired by <PERSON><PERSON>. Preserve the original composition and key forms. Render it with elegant linework, decorative floral motifs, and flowing curves, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/mucha.jpg"}, {"id": "banksy", "title": "<PERSON><PERSON>", "prompt": "Transform this image into a stencil street art style inspired by famous urban artists. Place the subject on a gritty city wall, add spray-paint textures, and incorporate elements of social commentary or visual irony. Use bold black-and-white stenciling with occasional splashes of color, and make the artwork feel like a clever intervention in public space.", "example_image": "/style-examples/banksy.jpg"}, {"id": "pop-art-comic", "title": "Pop Art Comic", "prompt": "Transform this image into a retro pop art comic book style. Use bold black outlines, vibrant flat colors, and prominent halftone dot shading. Add comic book elements like speech bubbles, dramatic expressions, and poster-like composition to evoke the feel of classic American comics from the 1960s.", "example_image": "/style-examples/pop-art-comic.jpg"}, {"id": "monet", "title": "Monet", "prompt": "Transform this image into Monet Impressionist style. Preserve the original composition and key forms. Render it with soft-focus brushwork, pastel color blends, and natural light effects, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/monet.jpg"}, {"id": "picasso", "title": "Picasso", "prompt": "Transform this image into a Picasso Cubist painting. Fragment the face and body into overlapping geometric planes, showing multiple perspectives at once. Use bold, expressive brushstrokes and a mix of natural and unexpected colors. Distort facial features and include painterly textures to evoke the feeling of <PERSON>'s early 20th-century cubist works.", "example_image": "/style-examples/picasso.jpg"}, {"id": "art-deco-poster", "title": "<PERSON>", "prompt": "Transform this image into an Art Deco poster style. Preserve the original composition and key forms. Render it with streamlined geometric shapes, gold accents, bold typography, and stylized ornamentation, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/art-deco-poster.jpg"}, {"id": "bauhaus-minimal", "title": "<PERSON><PERSON><PERSON>", "prompt": "Transform this image into a Bauhaus minimal style. Preserve the original composition and key forms. Render it with primary color blocks, simple typography, and functional geometric forms, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/bauhaus-minimal.jpg"}], "Aesthetic & Viral": [{"id": "vaporwave", "title": "Vaporwave", "prompt": "Transform this image into vaporwave style. Preserve the original composition and key forms. Render it with neon gradients, grid patterns, Greek busts, and 90s tech iconography, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/vaporwave.jpg"}, {"id": "cyberpunk-neon", "title": "Cyberpunk Neon", "prompt": "Transform this image into cyberpunk neon style. Preserve the original composition and key forms. Render it with vivid neon lights, futuristic elements, and rain-slick reflections, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/cyberpunk-neon.jpg"}, {"id": "holographic-prism", "title": "Holographic Prism", "prompt": "Transform this image into a holographic prism style. Preserve the original composition and key forms. Render it with shifting rainbow refractions, metallic sheens, and light-bend effects, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/holographic-prism.jpg"}, {"id": "glitch-chromatics", "title": "Glitch Chromatics", "prompt": "Transform this image into glitch chromatics. Preserve the original composition and key forms. Render it with digital artifacts, color channel separations, and pixel 'tears,' while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/glitch-chromatics.jpg"}, {"id": "neon-noir", "title": "Neon Noir", "prompt": "Transform this image into Neon Noir style. Preserve the original composition and key forms. Render it with atmospheric lighting, neon pinks, cyans, and urban textures, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/neon-noir.jpg"}, {"id": "liquid-metal", "title": "Liquid Metal", "prompt": "Transform this image into a liquid metal style. Preserve the original composition and key forms. Render it with chrome-like surfaces, fluid reflections, and smooth metallic melts, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/liquid-metal.jpg"}], "Retro & Experimental": [{"id": "prismatic-mosaic", "title": "Prismatic Mosaic", "prompt": "Transform this image into a prismatic mosaic style. Preserve the original composition and key forms. Render it with stained-glass shards, jewel-tone color segments, and black outlines, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/prismatic-mosaic.jpg"}, {"id": "kaleidoscopic-fractal", "title": "Kaleidoscopic Fractal", "prompt": "Transform this image into a kaleidoscopic fractal style. Preserve the original composition and key forms. Render it with repeating symmetrical patterns, psychedelic color blends, and radial symmetry, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/kaleidoscopic-fractal.jpg"}, {"id": "uv-blacklight-pop", "title": "UV Blacklight Pop", "prompt": "Transform this image into UV blacklight pop style. Preserve the original composition and key forms. Render it with deep blacks, fluorescent highlights, and glowing neon contrasts, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/uv-blacklight-pop.jpg"}, {"id": "thermal-vision", "title": "Thermal Vision", "prompt": "Transform this image into thermal-vision style. Preserve the original composition and key forms. Render it with false-color heat maps—reds, yellows, blues—and simulated infrared glow, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/thermal-vision.jpg"}, {"id": "8-bit-pixel-art", "title": "8-bit Pixel Art", "prompt": "Transform this image into 8-bit pixel art style. Preserve the original composition and key forms. Render it with blocky pixels, limited color palette, and chunky outlines, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/8-bit-pixel-art.jpg"}, {"id": "polaroid-film", "title": "Polaroid Film", "prompt": "Transform this image into Polaroid film style. Preserve the original composition and key forms. Render it with a white border, slight color fade, vignette edges, and film grain, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/polaroid-film.jpg"}, {"id": "retro-vhs", "title": "Retro VHS", "prompt": "Transform this image into Retro VHS style. Preserve the original composition and key forms. Render it with scan lines, color bleed, tracking distortion, and tape noise, while keeping important details like faces, architecture, or text recognizable.", "example_image": "/style-examples/retro-vhs.jpg"}]}