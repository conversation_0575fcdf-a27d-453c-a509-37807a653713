-- USERS table
ALTER POLICY "Users can read own data" ON users
USING (auth.uid() = id);

ALTER POLICY "Users can insert own data" ON users
WITH CHECK (auth.uid() = id);

ALTER POLICY "Users can update own data" ON users
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- IMAGES table
ALTER POLICY "Users can read own images" ON images
USING (auth.uid() = user_id);

ALTER POLICY "Users can read shared images" ON images
USING (is_shared = true);

ALTER POLICY "Users can insert own images" ON images
WITH CHECK (auth.uid() = user_id);

ALTER POLICY "Users can update own images" ON images
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

ALTER POLICY "Users can delete own images" ON images
USING (auth.uid() = user_id);

-- USER_FAVORITES table
ALTER POLICY "Users can read own favorites" ON user_favorites
USING (auth.uid() = user_id);

ALTER POLICY "Users can insert own favorites" ON user_favorites
WITH CHECK (auth.uid() = user_id);

ALTER POLICY "Users can delete own favorites" ON user_favorites
USING (auth.uid() = user_id); 