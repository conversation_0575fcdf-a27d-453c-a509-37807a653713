import { NextRequest, NextResponse } from "next/server";
import { generateImageHandler } from "@/lib/api/generateImageHandler";

export async function POST(request: NextRequest) {
  // Parse FormData and headers, then call the handler
  const formData = await request.formData();
  // Use next/headers for request headers context
  const { headers: getHeaders } = await import("next/headers");
  const requestHeaders = await getHeaders();
  const { status, body } = await generateImageHandler({
    formData,
    headers: requestHeaders,
  });
  return NextResponse.json(body, { status });
}
