{"name": "pxlmorph-ai", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next out node_modules/.cache", "type-check": "tsc --noEmit", "analyze": "node scripts/analyze-bundle.js", "analyze:bundle": "cross-env ANALYZE=true npm run build", "performance:report": "npm run build && npx lighthouse http://localhost:3000 --output=json --output-path=./lighthouse-report.json", "performance:ci": "npm run build && npx lighthouse http://localhost:3000 --output=json --only-categories=performance --output-path=./lighthouse-performance.json", "test": "jest --env=jsdom --config=jest.config.js", "format": "prettier --write .", "optimize:images": "node scripts/optimize-images.js", "prebuild": "npm run optimize:images"}, "dependencies": {"@next/bundle-analyzer": "^15.4.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@types/node": "24.1.0", "@types/react-window": "^1.8.8", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint-config-next": "15.4.3", "firebase": "^12.0.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "^15.4.3", "next-themes": "^0.4.6", "openai": "^5.10.2", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.3", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "babel-jest": "^30.0.5", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-plugin-tailwindcss": "^3.18.2", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jest-util": "^30.0.5", "node-fetch": "^3.3.2", "playwright": "^1.54.1", "prettier": "^3.6.2", "rimraf": "^6.0.1", "sharp": "^0.34.3", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}}