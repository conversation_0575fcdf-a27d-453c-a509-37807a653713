"use client";

import { useEffect } from "react";

export function ServiceWorkerRegistration() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === "undefined") return;

    // Register service worker only in production
    if (process.env.NODE_ENV === "production" && "serviceWorker" in navigator) {
      const registerSW = () => {
        navigator.serviceWorker
          .register("/sw.js")
          .then((_registration) => {
          })
          .catch((_registrationError) => {
          });
      };

      // Register after page load
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", registerSW);
      } else {
        registerSW();
      }
    } else if ("serviceWorker" in navigator) {
      // In development, unregister all service workers for fresh reloads
      navigator.serviceWorker.getRegistrations().then((regs) => {
        regs.forEach((reg) => reg.unregister());
      });
    }

    // Initialize performance monitoring
    import("@/lib/utils/performance")
      .then(({ monitorWebVitals }) => {
        monitorWebVitals();
      })
      .catch(() => {
        // Silently fail if performance monitoring fails
      });
  }, []);

  return null;
}
