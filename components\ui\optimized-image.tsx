
"use client";

import React, { useState, useCallback, memo } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: "blur" | "empty";
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  loading?: "lazy" | "eager";
  sizes?: string;
  fetchPriority?: "high" | "low" | "auto";
}

const OptimizedImage = memo<OptimizedImageProps>(
  ({
    src,
    alt,
    width = 400,
    height = 400,
    className,
    priority = false,
    quality = 85,
    placeholder = "empty",
    onLoad,
    onError,
    fallbackSrc,
    loading = "lazy",
    sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
    fetchPriority,
  }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [currentSrc, setCurrentSrc] = useState(src);

    const handleLoad = useCallback(() => {
      setIsLoading(false);
      onLoad?.();
    }, [onLoad]);

    const handleError = useCallback(() => {
      setIsLoading(false);
      setHasError(true);

      if (fallbackSrc && currentSrc !== fallbackSrc) {
        setCurrentSrc(fallbackSrc);
        setHasError(false); // Reset error state to allow fallback to load
        setIsLoading(true); // Show loader for fallback
      } else {
        onError?.();
      }
    }, [fallbackSrc, currentSrc, onError]);

    // Reset state when src changes
    React.useEffect(() => {
      if (src !== currentSrc) {
        setCurrentSrc(src);
        setIsLoading(true);
        setHasError(false);
      }
    }, [src, currentSrc]);

    if (hasError && !fallbackSrc) {
      return (
        <div
          className={cn(
            "flex items-center justify-center bg-gray-800 text-gray-400",
            className,
          )}
          style={{ width, height }}
        >
          <div className="text-center">
            <div className="mb-2 text-2xl">🖼️</div>
            <div className="text-sm">Image unavailable</div>
          </div>
        </div>
      );
    }

    return (
      <div
        className={cn("relative overflow-hidden image-container", className)}
      >
        <Image
          src={currentSrc}
          alt={alt}
          width={width}
          height={height}
          className={cn(
            // Remove transition-opacity to prevent flicker against parent animation
            isLoading ? "opacity-0" : "opacity-100",
            className, // Pass className to Image for object-fit and rounding
          )}
          priority={priority}
          quality={quality}
          placeholder={placeholder}
          onLoad={handleLoad}
          onError={handleError}
          {...(!priority && { loading })}
          sizes={sizes}
          fetchPriority={fetchPriority}
          style={{
            imageRendering: "crisp-edges",
            backfaceVisibility: "hidden",
            transform: "translateZ(0)",
            willChange: "auto",
          }}
        />

        {/* Loading skeleton */}
        {isLoading && (
          <div
            className="absolute inset-0 animate-pulse bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700"
            style={{ width, height }}
          />
        )}
      </div>
    );
  },
);

OptimizedImage.displayName = "OptimizedImage";

export { OptimizedImage };
