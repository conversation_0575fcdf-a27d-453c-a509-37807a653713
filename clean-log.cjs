// clean-log.js
const fs = require('fs');

if (process.argv.length < 4) {
  console.log('Usage: node clean-log.js <input-file> <output-file>');
  process.exit(1);
}

const [,, inputFile, outputFile] = process.argv;

const irrelevantPatterns = [
  /cache skip/i,
  /Compiled/i,
  /Compiling/i,
  /Cache skipped reason/i,
  /Cache skipped reason:/i,
  /runModuleExecutionHooks/i,
  /instantiateModule/i,
  /esmImport/i,
  /requireModule/i,
  /initializeModuleChunk/i,
  /resolveModuleChunk/i,
  /registerChunk/i,
  /loadScriptsInSequence/i,
  /appBootstrap/i,
  /exports.createFromReadableStream/i,
  /startReadingFromStream/i,
  /processFullStringRow/i,
  /processFullBinaryRow/i,
  /commonJsRequire/i,
  /dev-base.ts/i,
  /runtime-utils.ts/i,
  /runtime-backend-dom.ts/i,
  /runtime-base.ts/i,
  /dev-backend-dom.ts/i,
  /app-next-turbopack.ts/i,
  /app-bootstrap.ts/i,
  /app-index.tsx/i,
  /app-client/i,
  /ecmascript/i,
  /Understand this error/i,
  /Understand this warning/i,
  /turbopack-hot-reloader-common.ts/i,
  /report-hmr-latency.ts/i,
  /Fast Refresh/i,
  /HMR/i,
  /Promise.then/i,
  /progress @/i,
  /warn @/i,
  /deprecateSimple/i,
  /defineLocale/i,
  /getSetGlobalLocale/i,
  /Deprecation warning/i,
  /metricsAlerts.api-/i,
  /PriceTrackNotifications.view-/i,
  /NaNms/i,
  /Grid render: \[\]/i, // Only keep the first occurrence
];

const lines = fs.readFileSync(inputFile, 'utf-8').split('\n');
const seen = new Set();
const cleaned = [];

for (const line of lines) {
  if (!line.trim()) continue; // skip empty lines
  if (irrelevantPatterns.some((pat) => pat.test(line))) continue;
  if (seen.has(line)) continue;
  seen.add(line);
  cleaned.push(line);
}

fs.writeFileSync(outputFile, cleaned.join('\n'), 'utf-8');
console.log(`Cleaned log written to ${outputFile}`);
