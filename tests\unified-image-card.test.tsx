// Jest test file for UnifiedImageCard
// Requires: @testing-library/react, @testing-library/jest-dom, @types/jest
// Run: npm install --save-dev @testing-library/react @testing-library/jest-dom @types/jest

import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom";
import { UnifiedImageCard } from "../components/gallery/unified-image-card";
import { UserProvider } from "../lib/contexts/UserContext";
import { StylesProvider } from "../lib/contexts/StylesContext";
import userEvent from "@testing-library/user-event";

// Mock IntersectionObserver for jsdom
// @ts-ignore
global.IntersectionObserver = class {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

const mockUser = { id: "user1", email: "<EMAIL>" };

// Mock dependencies
jest.mock("next/image", () => {
  return {
    __esModule: true,
    default: (props: any) => {
      const { priority, fill, ...rest } = props;
      return require("react").createElement("img", rest);
    },
  };
});
jest.mock("../components/ui/delete-confirmation-modal", () => ({
  DeleteConfirmationModal: () => <div data-testid="delete-modal" />,
}));
jest.mock("../components/gallery/unified-image-modal", () => ({
  UnifiedImageModal: () => <div data-testid="image-modal" />,
}));
// Mock ESM-only modules
jest.mock("@/lib/supabase", () => ({
  supabase: {
    auth: {
      getSession: jest.fn(() =>
        Promise.resolve({
          data: {
            session: {
              user: { id: "user1", email: "<EMAIL>" },
              access_token: "fake-token",
            },
          },
        }),
      ),
      refreshSession: jest.fn(),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } },
      })),
      signOut: jest.fn(),
    },
  },
}));

// Update the test prompt and expected text to match the actual component output
const testImage = {
  id: "1",
  url: "test-url",
  prompt: "Test prompt for image card",
  style: "Test Style", // Changed from object to string
  user: { id: "user1", name: "Test User" },
  created_at: "2023-01-01T00:00:00Z",
  like_count: 0,
  liked_by_user: false,
  user_id: "user1",
  image_url: "test-url",
  is_shared: false,
  updated_at: "2023-01-01T00:00:00Z",
};

function Providers({ children }: { children: React.ReactNode }) {
  return (
    <UserProvider>
      <StylesProvider>{children}</StylesProvider>
    </UserProvider>
  );
}

describe("UnifiedImageCard", () => {
  it("renders without crashing", async () => {
    let container: HTMLElement | undefined = undefined;
    await act(async () => {
      const result = render(
        <Providers>
          <UnifiedImageCard image={testImage} />
        </Providers>,
      );
      container = result.container;
    });
    // Debug: Print the rendered DOM
    console.log("Rendered DOM:", container!.innerHTML);
    // Wait for the component to finish loading and render the button
    await waitFor(() => {
      expect(
        screen.getByRole("button", { name: /open image modal/i }),
      ).toBeInTheDocument();
    });
  });

  it("renders the image card with correct style", () => {
    render(
      <UserProvider initialUser={{
        id: "user1",
        app_metadata: {},
        user_metadata: {},
        aud: "authenticated",
        created_at: "2023-01-01T00:00:00Z"
      }}>
        <StylesProvider>
          <UnifiedImageCard image={testImage} />
        </StylesProvider>
      </UserProvider>
    );
    expect(screen.getByText(/test style/i)).toBeInTheDocument();
  });

  it("shows detailed variant content", () => {
    render(
      <UserProvider initialUser={{
        id: "user1",
        app_metadata: {},
        user_metadata: {},
        aud: "authenticated",
        created_at: "2023-01-01T00:00:00Z"
      }}>
        <StylesProvider>
          <UnifiedImageCard image={testImage} variant="detailed" />
        </StylesProvider>
      </UserProvider>
    );
    expect(screen.getByText(/test style/i)).toBeInTheDocument();
    expect(screen.getByText(/ai generated/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /view full size/i })).toBeInTheDocument();
  });

  // More prop variation and user interaction tests to be added
});
