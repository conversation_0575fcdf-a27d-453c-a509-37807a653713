import { test, expect } from "@playwright/test";

function randomEmail() {
  return `testuser+${Date.now()}@example.com`;
}

const TEST_PASSWORD = "TestPassword123!";

test.describe("Auth Flow", () => {
  test("signup flow shows confirmation message", async ({ page }) => {
    const email = randomEmail();
    await page.goto("/login");
    await page.getByTestId("tab-signup").click();
    await page.getByLabel(/Email/i).fill(email);
    await page.getByLabel(/Password/i).fill(TEST_PASSWORD);
    await page.getByTestId("btn-signup").click();
    // Wait for success or error
    await expect(page.getByTestId("auth-success")).toBeVisible({ timeout: 10000 });
  });

  test("login with invalid credentials shows error", async ({ page }) => {
    await page.goto("/login");
    await page.getByTestId("tab-signin").click();
    await page.getByLabel(/Email/i).fill("<EMAIL>");
    await page.getByLabel(/Password/i).fill("wrongpassword");
    await page.getByTestId("btn-signin").click();
    await expect(page.getByTestId("auth-error")).toBeVisible({ timeout: 10000 });
  });

  test("login with valid credentials redirects to studio", async ({ page }) => {
    const email = process.env.PLAYWRIGHT_TEST_EMAIL || "<EMAIL>";
    const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "TestPassword123!";
    await page.goto("/login");
    await page.getByTestId("tab-signin").click();
    await page.getByLabel(/Email/i).fill(email);
    await page.getByLabel(/Password/i).fill(password);
    await page.getByTestId("btn-signin").click();
    await expect(page).toHaveURL(/studio/);
    await expect(page.getByRole("heading", { name: /Studio/i })).toBeVisible();
  });

  test("session persists after reload", async ({ page }) => {
    const email = process.env.PLAYWRIGHT_TEST_EMAIL || "<EMAIL>";
    const password = process.env.PLAYWRIGHT_TEST_PASSWORD || "TestPassword123!";
    await page.goto("/login");
    await page.getByTestId("tab-signin").click();
    await page.getByLabel(/Email/i).fill(email);
    await page.getByLabel(/Password/i).fill(password);
    await page.getByTestId("btn-signin").click();
    await expect(page).toHaveURL(/studio/);
    await page.reload();
    await expect(page).toHaveURL(/studio/);
    await expect(page.getByRole("heading", { name: /Studio/i })).toBeVisible();
  });
}); 