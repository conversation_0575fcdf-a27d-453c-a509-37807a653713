import { createClient } from "@supabase/supabase-js";
import { Database } from "@/lib/database.types";

export async function imageLikeHandler({
  imageId,
  headers,
}: {
  imageId: string;
  headers: Headers;
}): Promise<{ status: number; body: any }> {
  if (!imageId) {
    return { status: 400, body: { error: "Image ID is required" } };
  }
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl || !supabaseServiceKey) {
    return {
      status: 500,
      body: {
        error: "Server configuration error: Missing Supabase credentials",
      },
    };
  }
  const authHeader = headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { status: 401, body: { error: "Unauthorized" } };
  }
  const token = authHeader.replace("Bearer ", "");
  // Inline initialization per request
  const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
  const {
    data: { user },
    error: sessionError,
  } = await supabase.auth.getUser(token);
  if (sessionError || !user) {
    return { status: 401, body: { error: "Unauthorized" } };
  }
  const userId = user.id;
  try {
    const { data: existingLike, error: findError } = await supabase
      .from("likes")
      .select("id")
      .eq("image_id", imageId)
      .eq("user_id", userId)
      .maybeSingle();
    if (findError && findError.code !== "PGRST116") {
      return {
        status: 500,
        body: { error: "Database error finding like: " + findError.message },
      };
    }
    let liked: boolean;
    if (existingLike) {
      const { error: deleteError } = await supabase
        .from("likes")
        .delete()
        .eq("user_id", userId)
        .eq("image_id", imageId);
      if (deleteError) {
        return {
          status: 500,
          body: { error: "Failed to unlike image: " + deleteError.message },
        };
      }
      liked = false;
    } else {
      const { error: insertError } = await supabase
        .from("likes")
        .insert({ user_id: userId, image_id: imageId });
      if (insertError) {
        return {
          status: 500,
          body: { error: "Failed to like image: " + insertError.message },
        };
      }
      liked = true;
    }
    const { data: imageDetails, error: imageError } = await supabase
      .from("images")
      .select("like_count")
      .eq("id", imageId)
      .single();
    if (imageError || !imageDetails) {
      return {
        status: 200,
        body: {
          liked,
          like_count: null,
          warning: "Could not fetch updated like count",
        },
      };
    }
    return {
      status: 200,
      body: { liked, like_count: imageDetails.like_count },
    };
  } catch (error: any) {
    return {
      status: 500,
      body: { error: "An unexpected error occurred: " + error.message },
    };
  }
}
