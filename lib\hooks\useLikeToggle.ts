import { useCallback, useState } from "react";
import { apiService } from "@/lib/api/apiService";
import { errorHandler } from "@/lib/utils/errorHandler";
import { showLikeToast } from "@/lib/utils/toast";
import { supabase } from "@/lib/supabase";

/**
 * useLikeToggle - Hook for like/unlike logic with optimistic update, API, and toast feedback.
 * @param {Object} params - Parameters for the hook.
 * @returns {Object} { handleLikeToggle, isLiking }
 */
export function useLikeToggle({
  imageId,
  likeCount,
  userLiked,
  mutateLikeStatus,
  isOwnImage,
  user,
  isLoadingLikeStatus,
}: {
  imageId: string;
  likeCount: number;
  userLiked: boolean;
  mutateLikeStatus: (data: any, shouldRevalidate: boolean) => void;
  isOwnImage: boolean;
  user: any;
  isLoadingLikeStatus: boolean;
}) {
  const [isLiking, setIsLiking] = useState(false);

  const handleLikeToggle = useCallback(
    async (e: React.MouseEvent) => {
      e.stopPropagation();
      if (!user) {
        errorHandler("Please log in to like images.", { userMessage: "Please log in to like images." });
        return;
      }
      if (isOwnImage) {
        errorHandler("You cannot like your own image.", { userMessage: "You cannot like your own image." });
        return;
      }
      if (isLiking || isLoadingLikeStatus) return;
      setIsLiking(true);
      const previousLikeStatus = { like_count: likeCount, user_liked: userLiked };
      const newLikedStatus = !userLiked;
      const newLikeCount = newLikedStatus ? likeCount + 1 : likeCount - 1;
      mutateLikeStatus(
        { like_count: newLikeCount, user_liked: newLikedStatus },
        false,
      );
      showLikeToast(newLikedStatus); // Show toast immediately
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session)
          throw new Error("Authentication error. Please log in again.");
        const { data: result, error } = await apiService.post<any>(
          `/api/images/${imageId}/like`,
          {},
          {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.access_token}`,
          },
        );
        if (error)
          throw new Error(error || "Failed to update like status");
        mutateLikeStatus(
          { like_count: result.like_count, user_liked: result.liked },
          true,
        );
      } catch (error: any) {
        mutateLikeStatus(previousLikeStatus, false);
        errorHandler(error, { userMessage: "Could not update like status." });
      } finally {
        setIsLiking(false);
      }
    },
    [user, isOwnImage, isLiking, isLoadingLikeStatus, likeCount, userLiked, mutateLikeStatus, imageId],
  );

  return { handleLikeToggle, isLiking };
} 