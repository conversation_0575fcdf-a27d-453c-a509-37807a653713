/*
  postcss
  Tip: For best experience, install the Tailwind CSS IntelliSense VS Code extension.
  This comment helps VS Code recognize Tailwind at-rules and suppresses unknown at-rule warnings.
*/
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Mobile navbar height - single source of truth */
  --mobile-navbar-height: 80px;
  --mobile-navbar-total-height: calc(var(--mobile-navbar-height) + env(safe-area-inset-bottom, 0px));
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --radius: 0.75rem;
  }
  .bg-app-gradient {
    @apply bg-gradient-to-br from-violet-500 via-blue-500 to-cyan-400;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    /* Global text selection and cursor rules */
    user-select: none;
    cursor: default;
    scrollbar-gutter: stable;
  }

  /* Make interactive elements selectable and use pointer cursor */
  a,
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  select:enabled,
  textarea:enabled,
  [tabindex]:not([tabindex="-1"]) {
    user-select: text;
    cursor: pointer;
  }

  /* Ensure form elements are properly interactive */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="url"],
  input[type="tel"],
  input[type="number"],
  textarea,
  select {
    cursor: text;
  }
}

/* Suppress hydration warnings */
.suppress-hydration-warning {
  -webkit-text-size-adjust: 100%;
}

/* Hide scrollbar by default for mobile/tablet */
::-webkit-scrollbar {
  display: none;
  width: 0px;
}
body {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Show and style scrollbar for desktop screens */
@media (min-width: 1024px) {
  ::-webkit-scrollbar {
    display: block;
    width: 6px;
  }
  body {
    -ms-overflow-style: auto;
    scrollbar-width: auto;
  }
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  ::-webkit-scrollbar-thumb {
    @apply bg-gradient-to-b from-violet-500 to-blue-500 rounded-full;
  }
  ::-webkit-scrollbar-thumb:hover {
    @apply from-violet-600 to-blue-600;
  }
}

/* Enhanced page transitions */
.page-transition {
  animation: fadeInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.stagger-animation > * {
  animation: fadeInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  opacity: 0;
}

.stagger-animation > *:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-animation > *:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-animation > *:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-animation > *:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-animation > *:nth-child(5) {
  animation-delay: 0.5s;
}
.stagger-animation > *:nth-child(6) {
  animation-delay: 0.6s;
}

/* Enhanced glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.glass-card {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.12) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  backdrop-filter: blur(24px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
  border-radius: 1.25rem;
  border: none !important;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, box-shadow;
}


.glass-card:hover {
  box-shadow: 0 16px 40px 0 rgba(139, 92, 246, 0.18);
}

/* Ensure images inside glass cards remain sharp during hover */
.glass-card img,
.glass-card [data-nextjs-image] {
  image-rendering: crisp-edges !important;
  backface-visibility: hidden !important;
  transform: translateZ(0) !important;
  will-change: auto !important;
}

/* Enhanced gradient text */
.gradient-text {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 50%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.gradient-text-danger {
  background: linear-gradient(90deg, #dc2626 0%, #fb923c 60%, #f87171 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.hover-lift:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 12px 32px rgba(139, 92, 246, 0.18);
}

/* Enhanced button glow effect */
.btn-glow {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(139, 92, 246, 0.18),
    transparent
  );
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-glow:hover::before {
  left: 100%;
}

.btn-glow:hover {
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.22);
  transform: translateY(-2px) scale(1.02);
}

.btn-glow-red {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-glow-red::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 0, 0, 0.12),
    transparent
  );
  transition: left 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-glow-red:hover::before {
  left: 100%;
}

.btn-glow-red:hover {
  box-shadow: 0 0 16px 2px rgba(255, 0, 0, 0.25);
  transform: translateY(-2px) scale(1.02);
}

/* Enhanced loading animation */
.pulse-glow {
  animation: pulseGlow 2.5s infinite;
}

@keyframes pulseGlow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
    transform: scale(1.05);
  }
}

/* Enhanced image grid animations */
.image-grid-item {
  transform: scale(0.8) translateY(20px);
  opacity: 0;
}

@keyframes scaleIn {
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* Enhanced navigation transitions */
.nav-transition {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Enhanced modal animations */
.modal-overlay {
  animation: fadeIn 0.4s ease-out;
}

.modal-content {
  animation: slideInUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced loading shimmer effect */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced micro-interactions */
.micro-bounce {
  transition: transform 0.1s ease-out;
}

.micro-bounce:active {
  transform: scale(0.95);
}

@keyframes microBounce {
  0% { transform: scale(0.98); }
  40% { transform: scale(1.04); }
  60% { transform: scale(0.99); }
  80% { transform: scale(1.01); }
  100% { transform: scale(1); }
}
.micro-bounce {
  animation: microBounce 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.fade-in {
  animation: fadeIn 0.7s ease;
}

/* Enhanced focus states */
.focus-ring {
  transition: box-shadow 0.2s ease-out;
}

.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.5);
}

/* Enhanced text utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced layout utilities */
.prevent-layout-shift {
  contain: layout style paint;
}

/* Enhanced card entrance animations */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card-entrance {
  animation: cardEntrance 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Enhanced state transitions */
.state-transition {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Enhanced progress animations */
.progress-glow {
  background: linear-gradient(90deg, #8b5cf6, #3b82f6, #06b6d4);
  background-size: 200% 100%;
  animation: progressFlow 2s ease-in-out infinite;
}

@keyframes progressFlow {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Enhanced orbit animations */
@keyframes orbitSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.orbit-container {
  animation: orbitSpin 20s linear infinite;
}

/* Enhanced image fade-in */
.image-fade-in {
  animation: imageFadeIn 0.8s ease-out forwards;
}

@keyframes imageFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced error animations */
.error-bounce {
  animation: errorBounce 0.6s ease-out;
}

@keyframes errorBounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Enhanced skeleton loading */
.skeleton-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonPulse 1.5s ease-in-out infinite;
}

@keyframes skeletonPulse {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced responsive breakpoints */
@media (min-width: 768px) {
  .desktop-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .desktop-nav .nav-content {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .desktop-nav .nav-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
}

/* Enhanced tooltip animations */
@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.tooltip-enter {
  animation: tooltipSlideIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Mobile navigation styles - using CSS custom properties for consistent height */
@media (max-width: 768px) {
  .mobile-optimized {
    padding: 0.75rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Mobile navbar - single source of truth for dimensions */
  .mobile-bottom-nav {
    display: block !important;
    width: 100vw !important;
    max-width: 100vw !important;
    height: var(--mobile-navbar-total-height) !important;
    min-height: var(--mobile-navbar-total-height) !important;
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    -webkit-overflow-scrolling: auto;
    scroll-behavior: auto;
    border: none;
    outline: none;
    -webkit-user-select: none;
    user-select: none;
    box-sizing: border-box;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 99999 !important;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
    isolation: isolate;
    inset: auto 0 0 0 !important;
  }

  .mobile-bottom-nav * {
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    -webkit-user-select: none !important;
    user-select: none !important;
  }
  
  /* Ensure no borders or gaps in mobile nav containers */
  .mobile-bottom-nav {
    border: none !important;
    outline: none !important;
    margin: 0 !important;
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    box-sizing: border-box !important;
    width: 100% !important;
    max-width: 100% !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 50 !important;
  }

  .mobile-bottom-nav > div {
    border: none !important;
    outline: none !important;
    margin: 0 !important;
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    box-sizing: border-box !important;
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Global mobile navbar body adjustment - single source of truth */
  body:has(.mobile-bottom-nav) {
    overflow-x: hidden !important;
    padding-bottom: var(--mobile-navbar-total-height) !important;
  }  /* Prevent scrolling on mobile navigation specifically */
  .mobile-bottom-nav {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: auto !important;
  }
  
  .mobile-bottom-nav * {
    scroll-behavior: auto !important;
    -webkit-overflow-scrolling: auto !important;
  }
  
  /* Enhanced mobile nav glass effect for native mobile UX */
  .mobile-bottom-nav .glass {
    background: rgba(15, 23, 42, 0.98) !important;
    backdrop-filter: blur(24px) !important;
    -webkit-backdrop-filter: blur(24px) !important;
    border: none !important;
    outline: none !important;
    box-shadow: 0 -4px 32px rgba(0, 0, 0, 0.5), 0 -1px 0 rgba(255, 255, 255, 0.1) !important;
    margin: 0 !important;
    overflow: hidden !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    /* Native mobile safe area support */
    padding-bottom: env(safe-area-inset-bottom, 0px) !important;
    min-height: var(--mobile-navbar-total-height) !important;
  }
  
  /* Ensure flex container fits viewport */
  .mobile-bottom-nav .flex {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    flex-wrap: nowrap !important;
  }
  
  .mobile-bottom-nav button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    width: 25% !important;
    max-width: 25% !important;
    min-width: 0 !important;
    flex: 1 !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .mobile-bottom-nav button:active {
    -webkit-tap-highlight-color: transparent;
    border: none !important;
  }

  /* Prevent text selection on mobile navbar */
  .mobile-bottom-nav * {
    -webkit-user-select: none;
    user-select: none;
  }

  /* Remove any potential border styling from mobile navbar */
  .mobile-bottom-nav button {
    border: none !important;
  }

  /* Enhanced mobile nav animations */
  @keyframes mobile-nav-bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  .mobile-bottom-nav button:active {
    animation: mobile-nav-bounce 0.3s ease-out;
  }

  /* Improved glass morphism effect */
  .mobile-bottom-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }
}

/* Mobile navigation styles */
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom, 0px);
}

/* Ensure consistent main padding for mobile navigation */
@media (max-width: 768px) {
  main[style*="padding-bottom"] {
    padding-bottom: calc(96px + env(safe-area-inset-bottom, 0px)) !important;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-optimized {
    padding: 1rem;
  }
}

@media (min-width: 1025px) {
  .desktop-optimized {
    padding: 1.5rem;
  }
}

/* Enhanced accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card {
    background: rgba(139, 92, 246, 0.2);
  }

  .focus-ring:focus-visible {
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.8);
  }
}

.btn-primary-hero {
  @apply px-8 py-4 rounded-xl bg-gradient-to-r from-violet-500 to-blue-500 text-white font-bold text-lg btn-glow hover-lift shadow-xl;
}

/* Profile page animations */
@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-16px); }
  100% { transform: translateY(0); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes gradient-move {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-move {
  background: linear-gradient(270deg, #7c3aed, #2563eb, #06b6d4, #7c3aed);
  background-size: 400% 400%;
  animation: gradient-move 6s ease-in-out infinite;
}

/* Button glow effect */
.btn-glow:active {
  box-shadow: 0 0 0 6px rgba(139,92,246,0.18), 0 2px 12px 0 rgba(139,92,246,0.10);
  filter: brightness(1.08);
  transition: box-shadow 0.2s, filter 0.2s;
}

/* Enhanced button consistency */
.btn-consistent {
  @apply bg-black/70 backdrop-blur-sm text-white hover:bg-black/80 transition-all duration-200 h-9 rounded-full border border-white/20 shadow-lg focus-ring;
}

.btn-consistent-shared {
  @apply bg-emerald-500/70 hover:bg-emerald-500/80 border-emerald-400/30 text-emerald-100;
}

.btn-consistent-danger {
  @apply text-red-400 hover:bg-red-500/70 hover:text-red-300;
}

/* === GLOBAL UNSELECTABLE TEXT AND CURSOR (NO BEAM CURSOR, HAND CURSOR FOR ALL INTERACTIVE) === */
html,
body {
  user-select: none !important;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  max-width: 100%;
}

* {
  user-select: none !important;
}

body {
  overflow-y: scroll;
}

/* CRITICAL: Place pointer cursor rules LAST for maximum specificity and override protection */
[onclick],
[role="button"],
[tabindex]:not([tabindex="-1"]),
a,
button,
input[type="button"],
input[type="submit"],
select,
label,
summary,
.cursor-pointer {
  user-select: none !important;
  cursor: pointer !important;
}

button:disabled,
[aria-disabled="true"],
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed !important;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="url"],
input[type="tel"],
input[type="number"],
textarea:not([readonly]):not([disabled]) {
  cursor: text !important;
  user-select: text !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-text {
  cursor: text !important;
}

/* --- End of all styles --- */

/* Accessibility: Always show focus ring for keyboard navigation */
:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Global image sharpness and performance optimizations */
img,
image {
  image-rendering: crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure Next.js Image components are sharp */
.next-image-wrapper img {
  image-rendering: crisp-edges !important;
  backface-visibility: hidden !important;
  transform: translateZ(0) !important;
}

/* Optimize image containers */
.image-container {
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
}

.card-gradient-bg {
  transition:
    background 0.6s cubic-bezier(0.16, 1, 0.3, 1),
    background-size 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.card-gradient-bg .image-container img,
.card-gradient-bg .image-container [data-nextjs-image] {
  object-fit: contain !important;
  object-position: center !important;
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}

.style-card-image-container {
  aspect-ratio: 1 / 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #0f172a;
  position: relative;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 1.25rem !important;
  width: 100%;
  height: 100%;
}
.style-card-image-container img {
  object-fit: cover !important;
  object-position: center !important;
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
  display: block;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: inherit;
}

.style-card-hover {
  border-radius: 1.25rem !important;
  transition:
    border 0.35s cubic-bezier(0.16, 1, 0.3, 1),
    box-shadow 0.35s cubic-bezier(0.16, 1, 0.3, 1),
    background 0.7s cubic-bezier(0.16, 1, 0.3, 1),
    background-size 0.7s cubic-bezier(0.16, 1, 0.3, 1),
    transform 0.7s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: transform, box-shadow;
}

.style-card-hover:not(.style-card-selected):hover {
  box-shadow: 0 6px 36px 0 #a78bfa77, 0 2px 12px 0 #3b82f677 !important;
  background: linear-gradient(120deg, #a78bfa 0%, #60a5fa 100%) !important;
  background-size: 200% 200%;
  /* No animation on hover for a clean look */
  transform: scale(1.045) !important;
  will-change: transform;
  z-index: 1;
  border: none !important;
}

[data-radix-tooltip-content] {
  pointer-events: auto !important;
  z-index: 9999 !important;
}

/* Animated gradient for CTA and studio buttons */
.transform-btn-animated {
  background: linear-gradient(90deg, #8b5cf6, #2563eb, #06b6d4, #8b5cf6);
  background-size: 400% 400%;
  animation: gradientShift 3s ease-in-out infinite;
  border: 1px solid rgba(139,92,246,0.12);
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 8px #f87171);
  }
  50% {
    transform: scale(1.08);
    filter: drop-shadow(0 0 16px #f87171);
  }
}

.animate-warning-pulse {
  animation: warningPulse 1.5s infinite;
}

/* Floating animation for StudioHero icon */
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-16px);
  }
  100% {
    transform: translateY(0);
  }
}
.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Gradient move animation for backgrounds */
@keyframes gradient-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.animate-gradient-move {
  background: linear-gradient(
    270deg,
    #7c3aed,
    #2563eb,
    #06b6d4,
    #7c3aed
  );
  background-size: 400% 400%;
  animation: gradient-move 6s ease-in-out infinite;
}

/* Custom: Make StudioHero smaller on desktop */
@media (min-width: 1024px) {
  .studio-hero-desktop-scale {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    /* Optionally scale down further if needed */
    transform: scale(0.92);
  }
}

@keyframes studio-fadein {
  0% { opacity: 0; transform: translateY(32px) scale(0.98); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}
.animate-studio-fadein {
  animation: studio-fadein 0.7s cubic-bezier(0.4,0,0.2,1) both;
}

@keyframes app-entrance-fadein {
  0% { opacity: 0; transform: translateY(32px) scale(0.98); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}
.app-entrance-animate {
  animation: app-entrance-fadein 0.7s cubic-bezier(0.4,0,0.2,1) both;
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}
.animate-fadeOut {
  animation: fadeOut 0.7s cubic-bezier(0.4,0,0.2,1) forwards;
}

@keyframes spinner-spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spinner-spin {
  animation: spinner-spin 1s linear infinite;
}

.bg-spinner-gradient {
  background: conic-gradient(
    from 90deg at 50% 50%,
    #10b981 0deg,
    #a855f7 120deg,
    #2563eb 240deg,
    #10b981 360deg
  );
}
    
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out;
}
