
# Copilot Instructions for PxlMorph AI

## Project Overview
- **PxlMorph AI** is a Next.js app for AI-powered image generation, using Supabase for authentication/storage and OpenAI DALL-E 3 for image creation and editing.
- The codebase is designed for maintainability and security, with business logic in shared React hooks and utility modules.

## Architecture & Key Patterns
- **Frontend:** Next.js (App Router, TypeScript, Tailwind CSS, ShadCN UI). UI logic is modularized in `components/` (see `components/ui/`, `components/studio/`, etc.).
- **Backend:** Next.js API routes in `app/api/` (RESTful, JWT-authenticated), with server-side logic in `lib/`.
- **Database/Storage:** Supabase PostgreSQL (RLS enforced), private storage buckets (`private-images`).
- **AI Integration:** OpenAI DALL-E 3 for image generation and editing (see `lib/openai-service.ts`).
- **State/Feedback:** Centralized in `lib/hooks/` and `lib/utils/` for DRY, consistent UX.

## Critical Developer Workflows
- **Build:** `npm run build` (Next.js production build)
- **Dev:** `npm run dev` (hot reload, turbo mode)
- **Test:** `npm test` (Jest, see scripts for more)
- **Clean:** `npm run clean` (removes build/cache)
- **Static/JSON Update:** Always stop dev server, delete `.next`, then restart to pick up changes
- **Supabase Setup:** Run SQL migrations in `supabase/migrations/` and create the `private-images` bucket (see README)
- **Environment:** All secrets in `.env.local` (never commit)

## Project-Specific Conventions
- **Business logic:** Use shared hooks in `lib/hooks/` (e.g., `useImageDownload`, `useLikeToggle`, `useImageGeneration`, `useImageShare`, `useImageDelete`).
- **Formatting, toasts, errors:** Centralized in `lib/utils/` (`format.ts`, `toast.ts`, `errorHandler.ts`).
- **API endpoints:** Defined in `app/api/`, follow RESTful patterns, expect Supabase JWT auth. See README for endpoint details and request/response shapes.
- **Supabase:** All storage is private by default, RLS policies enforced, see `supabase/migrations/` for schema and policy examples.
- **Image storage:** Path format is `{user_id}/{timestamp}-{style}.png` in the `private-images` bucket.
- **UI:** Use Tailwind CSS and ShadCN UI components, follow patterns in `components/ui/` and `components/studio/`.
- **Testing:** Unit tests for hooks/utilities, integration tests for user flows (see `tests/`).
- **Security:** All API routes require session validation; never expose service role keys to the client.

## Integration Points & Data Flows
- **Supabase:** Used for auth, storage, and database (see `lib/supabase.ts`, `lib/supabaseClient.ts`).
- **OpenAI:** Used for image generation/editing (see `lib/openai-service.ts`).
- **Image Upload/Download:** All uploads go to Supabase Storage; downloads are handled server-side to avoid CORS issues (see `useImageDownload`).
- **RLS Policies:** All database/storage access is protected by Row Level Security; see `supabase/migrations/` for policy SQL.
- **API Data Flow:** Frontend calls API routes in `app/api/`, which validate session, process data, and interact with Supabase/OpenAI as needed.

## Examples & Patterns
- **Adding a new image action:** Create a hook in `lib/hooks/`, use utility functions from `lib/utils/`, and update API logic in `app/api/` if needed.
- **New UI:** Use Tailwind classes and reference existing components in `components/ui/` and `components/studio/`.
- **API Example:** See `/api/generate-image` and `/api/edit-image` for request/response structure and error handling.
- **Testing:** Add/extend tests in `tests/` for new hooks, utilities, or user flows.

## Advanced/Hidden Workflows & Gotchas
- **Static/JSON file changes:** Always stop the dev server and delete `.next` before restarting, or changes will not be picked up (see README).
- **Supabase RLS:** If you get unexplained 401/403 errors, check your RLS policies and JWT/session handling.
- **Image file structure:** All images must be stored as `{user_id}/{timestamp}-{style}.png` in the `private-images` bucket for correct access and cleanup.
- **Environment variables:** Never commit `.env.local`. Service role keys must only be used server-side.
- **SSR/Env issues:** Always test with `next build` and `next start` before deploying.

## References
- See `README.md` for environment setup, API details, and architecture.
- See `VibecodingNOTES/Official Documentation Next.js + Supabase.md` for quick links to docs and troubleshooting.

---

**When in doubt, follow the patterns in `lib/hooks/` and `lib/utils/`, and keep business logic DRY and centralized.**
