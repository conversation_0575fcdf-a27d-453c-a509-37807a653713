"use client";

import { useState, useTransition } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Wand2 } from "lucide-react";
import { toast } from "sonner";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { supabase } from "@/lib/supabase";

interface AuthFormProps {
  onAuthSuccess: () => void;
  onFadingOutDone?: () => void;
}

export function AuthForm({ onAuthSuccess, onFadingOutDone }: AuthFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState<"signin" | "signup">("signin");
  const { trackUserLogin, trackUserSignup, trackError } = useAnalytics();
  const [isPending, startTransition] = useTransition();
  const [success, setSuccess] = useState("");
  const [isFadingOut, setIsFadingOut] = useState(false);

  async function handleAuth(isSignUp: boolean, email: string, password: string) {
    setIsLoading(true);
    setError("");
    setSuccess("");
    try {
      console.log("[DEBUG] handleAuth called", { isSignUp, email, passwordLength: password.length });
      let result;
      if (isSignUp) {
        result = await supabase.auth.signUp({ email, password });
      } else {
        result = await supabase.auth.signInWithPassword({ email, password });
      }
      console.log("[DEBUG] Supabase auth result", result);
      if (result.error) {
        setError(result.error.message);
        trackError("auth_error", result.error.message, isSignUp ? "signup" : "login");
        setIsLoading(false);
        return;
      }
      if (isSignUp) {
        setSuccess("Check your email to confirm your account.");
        trackUserSignup("email");
        setIsLoading(false);
      } else {
        trackUserLogin("email");
        // Set flag for StudioPage to show welcome toast
        if (typeof window !== "undefined") {
          window.sessionStorage.setItem("showWelcomeToast", "1");
        }
        setIsFadingOut(true);
        // Wait for fade-out animation, then call onFadingOutDone
        setTimeout(() => {
          setIsLoading(false);
          if (onFadingOutDone) onFadingOutDone();
          window.location.href = "/studio";
        }, 500); // match fade-out duration
      }
    } catch (err) {
      console.error("[DEBUG] Unexpected error in handleAuth", err);
      const errorMessage = "An unexpected error occurred";
      setError(errorMessage);
      trackError("auth_unexpected", errorMessage, isSignUp ? "signup" : "login");
      setIsLoading(false);
    }
  }

  return (
    <div className={`w-full ${isFadingOut ? 'animate-fadeOut' : ''}`}>
      <div className="page-transition w-full max-w-md">
        {/* Background decoration */}
        <div className="pointer-events-none absolute inset-0 overflow-hidden">
          <div className="absolute left-1/4 top-1/4 size-64 rounded-full bg-violet-500/10 blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 size-64 rounded-full bg-blue-500/10 blur-3xl"></div>
        </div>

        <Card className="glass-card card-entrance">
          <CardHeader className="card-entrance space-y-4 text-center">
            <div className="flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 animate-pulse rounded-full bg-gradient-to-r from-violet-500 to-blue-500 opacity-30 blur-xl"></div>
                <div className="relative rounded-full bg-gradient-to-r from-violet-500 to-blue-500 p-4">
                  <Wand2 className="size-8 text-white" />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <CardTitle className="gradient-text pb-1 text-4xl font-extrabold leading-tight drop-shadow-lg">
                PxlMorph AI
              </CardTitle>
              <CardDescription className="text-lg font-medium text-blue-200/90 drop-shadow-sm">
                Unleash your creativity with AI-powered art
              </CardDescription>
            </div>
            <div className="flex items-center justify-center gap-2 text-base font-semibold text-teal-300/80">
              <span className="text-xs font-light text-slate-400/80">
                Stunning visuals, effortless creation
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="signin"
              className="w-full"
              onValueChange={(v) => setActiveTab(v as "signin" | "signup")}
            >
              <TabsList className="relative grid w-full grid-cols-2 overflow-hidden rounded-xl bg-slate-800/50">
                {/* Animated sliding pill highlight */}
                <span
                  className={`absolute left-1 top-1 z-10 h-[calc(100%-0.5rem)] w-[calc(50%-0.25rem)] rounded-xl bg-opacity-60 bg-gradient-to-r from-violet-500/60 to-blue-500/60 backdrop-blur-md transition-all duration-300 ${activeTab === "signup" ? "translate-x-full" : ""}`}
                  aria-hidden="true"
                />
                <TabsTrigger
                  value="signin"
                  className="relative z-20 cursor-pointer bg-transparent font-semibold transition-colors hover:bg-slate-700/40 hover:text-white data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-slate-300"
                  onClick={() => setActiveTab("signin")}
                  data-testid="tab-signin"
                >
                  Sign In
                </TabsTrigger>
                <TabsTrigger
                  value="signup"
                  className="relative z-20 cursor-pointer bg-transparent font-semibold transition-colors hover:bg-slate-700/40 hover:text-white data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-slate-300"
                  onClick={() => setActiveTab("signup")}
                  data-testid="tab-signup"
                >
                  Sign Up
                </TabsTrigger>
              </TabsList>

              <TabsContent value="signin" className="mt-6 space-y-4">
                <AuthTabContent
                  title="Welcome back, creator"
                  buttonText="Sign In"
                  onSubmit={(email, password) => {
                    startTransition(() => handleAuth(false, email, password));
                  }}
                  isLoading={isLoading || isPending}
                  testId="btn-signin"
                />
              </TabsContent>

              <TabsContent value="signup" className="mt-6 space-y-4">
                <AuthTabContent
                  title="Begin your artistic journey"
                  buttonText="Create Account"
                  onSubmit={(email, password) => {
                    startTransition(() => handleAuth(true, email, password));
                  }}
                  isLoading={isLoading || isPending}
                  testId="btn-signup"
                />
              </TabsContent>
            </Tabs>

            {error && (
              <Alert
                className="mt-4 border-red-500/30 bg-red-500/10"
                variant="destructive"
                data-testid="auth-error"
              >
                <AlertDescription className="text-red-200">
                  {error}
                </AlertDescription>
              </Alert>
            )}
            {success && (
              <Alert className="mt-4 border-green-500/30 bg-green-500/10" variant="default" data-testid="auth-success">
                <AlertDescription className="text-green-200">{success}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

interface AuthTabContentProps {
  title: string;
  buttonText: string;
  onSubmit: (email: string, password: string) => void;
  isLoading: boolean;
  testId: string;
}

function AuthTabContent({
  title,
  buttonText,
  onSubmit,
  isLoading,
  testId,
}: AuthTabContentProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(email, password);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-slate-200">{title}</h3>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-slate-300">
            Email
          </Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="border-slate-700 bg-slate-800/50 text-white placeholder:text-slate-500 focus:border-violet-500"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-slate-300">
            Password
          </Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="border-slate-700 bg-slate-800/50 text-white placeholder:text-slate-500 focus:border-violet-500"
            required
          />
        </div>
      </div>

      <Button
        type="submit"
        className="btn-glow hover-lift w-full rounded-xl bg-gradient-to-r from-violet-500 to-blue-500 py-6 text-white hover:from-violet-600 hover:to-blue-600"
        disabled={isLoading}
        data-testid={testId}
      >
        {isLoading ? (
          <span className="mr-2 inline-block size-5 animate-spin rounded-full border-2 border-white border-t-transparent align-middle" />
        ) : null}
        {buttonText}
      </Button>
    </form>
  );
}
