// @ts-nocheck
import { imageProxyHandler } from "@/lib/api/imageProxyHandler";
import { Buffer } from "buffer";

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock Supabase
const mockGetUser = jest.fn(() =>
  Promise.resolve({ data: { user: { id: "user123" } }, error: null }),
);
const mockSingle = jest.fn(() =>
  Promise.resolve({
    data: {
      image_url: "private-images/user123/file.png",
      user_id: "user123",
      is_shared: true,
    },
    error: null,
  }),
);
const mockFrom = jest.fn(() => ({
  select: jest.fn(() => ({
    eq: jest.fn().mockReturnThis(),
    single: mockSingle,
  })),
  eq: jest.fn().mockReturnThis(),
}));
const mockDownload = jest.fn(() =>
  Promise.resolve({
    data: {
      arrayBuffer: () => Promise.resolve(new Uint8Array([1, 2, 3]).buffer),
      type: "image/png",
    },
    error: null,
  }),
);
const mockStorage = {
  from: jest.fn(() => ({ download: mockDownload })),
  download: mockDownload,
};
const mockCreateClient = () => ({
  auth: { getUser: mockGetUser },
  from: mockFrom,
  storage: mockStorage,
});

global.fetch = jest.fn();

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("imageProxyHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns 401 if no auth header", async () => {
    const headers = new Headers();
    const { status, body } = await imageProxyHandler({
      imageId: "uuid",
      headers,
    });
    // Debug output
    console.log("401 test result:", { status, body });
    expect(status).toBe(401);
    expect(body.error).toMatch(/Authentication required/);
  });

  it("returns 200 and binary body on success", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const {
      status,
      body,
      headers: respHeaders,
    } = await imageProxyHandler({ imageId: "img1", headers });
    // Debug output
    console.log("200 test result:", { status, body, respHeaders });
    expect(status).toBe(200);
    expect(body).toBeInstanceOf(Buffer);
    expect(respHeaders["Content-Type"]).toBe("image/png");
  });
});
