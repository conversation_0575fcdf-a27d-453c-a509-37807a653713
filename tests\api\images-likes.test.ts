// @ts-nocheck
import { imageLikesHandler } from "@/lib/api/imageLikesHandler";
import { Buffer } from "buffer";

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock Supabase
const mockGetUser = jest.fn(() =>
  Promise.resolve({ data: { user: { id: "user123" } }, error: null }),
);
const mockSingle = jest.fn(() =>
  Promise.resolve({ data: { like_count: 5 }, error: null }),
);
const mockMaybeSingle = jest.fn(() =>
  Promise.resolve({ data: { id: "like1" }, error: null }),
);
const mockEq2 = jest.fn(() => ({
  eq: mockEq2,
  maybeSingle: mockMaybeSingle,
  single: mockSingle,
}));
const mockEq1 = jest.fn(() => ({
  eq: mockEq2,
  maybeSingle: mockMaybeSingle,
  single: mockSingle,
}));
const mockSelect = jest.fn(() => ({ eq: mockEq1 }));
const mockFrom = jest.fn(() => ({ select: mockSelect }));
const mockCreateClient = () => ({
  auth: { getUser: mockGetUser },
  from: mockFrom,
});

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("imageLikesHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns 400 if imageId is missing", async () => {
    const headers = new Headers();
    const { status, body } = await imageLikesHandler({ imageId: "", headers });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Image ID is required/);
  });

  it("returns 200 and like_count on success", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await imageLikesHandler({
      imageId: "img1",
      headers,
    });
    console.log("imageLikesHandler like_count success:", { status, body });
    expect(status).toBe(200);
    expect(body.like_count).toBe(5);
    // user_liked is null in this mock
  });
});
