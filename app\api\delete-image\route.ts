import { NextRequest, NextResponse } from "next/server";
import { deleteImageHand<PERSON> } from "@/lib/api/deleteImageHandler";

export async function DELETE(request: NextRequest) {
  const requestHeaders = Object.fromEntries(request.headers.entries());
  const body = await request.json();
  const env = process.env;
  // Supabase should be passed in real route, but for now pass undefined (handler will error if not set)
  const result = await deleteImageHandler({
    headers: requestHeaders,
    body,
    env,
  });
  return NextResponse.json(result.body, { status: result.status });
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Authorization, Content-Type",
    },
  });
}
