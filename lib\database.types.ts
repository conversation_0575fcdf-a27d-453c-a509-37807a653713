export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      images: {
        Row: {
          id: string;
          user_id: string;
          image_url: string;
          style: string;
          prompt: string;
          inspiration_image_url: string | null;
          is_shared: boolean;
          created_at: string;
          updated_at: string;
          shared_at: string | null;
          like_count: number; // Added
          // Add other existing columns as needed, ensure they match actual schema
        };
        Insert: {
          id?: string;
          user_id: string;
          image_url: string;
          style: string;
          prompt: string;
          inspiration_image_url?: string | null;
          is_shared?: boolean;
          created_at?: string;
          updated_at?: string;
          shared_at?: string | null;
          like_count?: number; // Added
        };
        Update: {
          id?: string;
          user_id?: string;
          image_url?: string;
          style?: string;
          prompt?: string;
          inspiration_image_url?: string | null;
          is_shared?: boolean;
          created_at?: string;
          updated_at?: string;
          shared_at?: string | null;
          like_count?: number; // Added
        };
        Relationships: [
          {
            foreignKeyName: "images_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users"; // Assuming a 'users' table in 'public' schema for app users
            referencedColumns: ["id"];
          },
        ];
      };
      likes: {
        // New table
        Row: {
          id: string;
          image_id: string;
          user_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          image_id: string;
          user_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          image_id?: string;
          user_id?: string;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "likes_image_id_fkey";
            columns: ["image_id"];
            referencedRelation: "images";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "likes_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users"; // Referring to auth.users, but Supabase types often map this to a public.users view or similar
            referencedColumns: ["id"];
          },
        ];
      };
      user_favorites: {
        // Example of another table, ensure it's accurate if used
        Row: {
          id: string;
          user_id: string;
          style_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          style_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          style_id?: string;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_favorites_style_id_fkey";
            columns: ["style_id"];
            referencedRelation: "styles"; // Assuming a styles table
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_favorites_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      // Define other tables as needed, e.g., a 'users' table in public schema if it exists
      // For auth.users, Supabase client handles its types internally but often you might have a public.users table/view.
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      update_like_count: {
        // The trigger function
        Args: Record<string, unknown>; // Adjust if it takes specific args, though trigger functions usually don't directly.
        Returns: unknown; // Trigger functions often return 'trigger' or 'void'
      };
      // Define other functions as needed
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
