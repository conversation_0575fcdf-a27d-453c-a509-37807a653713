"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { getFacebookShareUrl, getTwitterShareUrl } from "@/lib/utils/format";
import { useImageShare } from "@/lib/hooks/useImageShare";
import { Copy, Facebook, Send, Twitter } from "lucide-react";
import { useState } from "react";

interface SocialSharePopoverProps {
  url: string;
  title: string;
}

export function SocialSharePopover({ url, title }: SocialSharePopoverProps) {
  const { share, isSharing } = useImageShare();
  const [isOpen, setIsOpen] = useState(false);

  const handleCopy = () => {
    share(url);
    setIsOpen(false);
  };

  const twitterUrl = getTwitterShareUrl(url, title);
  const facebookUrl = getFacebookShareUrl(url);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          aria-label="Share to Socials"
          className="size-8 border border-slate-700/50 bg-slate-900/80 text-white backdrop-blur-md transition-all duration-200 hover:bg-slate-800/90 hover:text-white"
          onClick={(e) => e.stopPropagation()}
        >
          <Send className="size-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-2" onClick={(e) => e.stopPropagation()}>
        <div className="grid gap-2">
          <div className="pb-1 text-center text-sm font-medium text-muted-foreground">
            Share via
          </div>
          <Separator />
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            asChild
          >
            <a href={twitterUrl} target="_blank" rel="noopener noreferrer" onClick={() => setIsOpen(false)}>
              <Twitter className="size-4" />
              Twitter / X
            </a>
          </Button>
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            asChild
          >
            <a href={facebookUrl} target="_blank" rel="noopener noreferrer" onClick={() => setIsOpen(false)}>
              <Facebook className="size-4" />
              Facebook
            </a>
          </Button>
          <Separator />
          <Button
            variant="ghost"
            className="w-full justify-start gap-2"
            onClick={handleCopy}
            disabled={isSharing}
          >
            <Copy className="size-4" />
            {isSharing ? 'Copying...' : 'Copy Link'}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
