"use client";

export default function GlobalError({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-slate-900 text-white">
      <h1 className="mb-4 text-3xl font-bold">Something went wrong</h1>
      <p className="mb-4">{error?.message || "An unknown error occurred."}</p>
      <button
        className="rounded bg-blue-600 px-4 py-2 hover:bg-blue-700"
        onClick={() => reset()}
      >
        Try Again
      </button>
    </div>
  );
} 