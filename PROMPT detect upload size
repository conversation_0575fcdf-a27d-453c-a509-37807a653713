1. **Parse the uploaded file**

   * In your Next.js API route, read the incoming image (e.g. via `formidable` or a `fetch` + `Blob`).
   * Save it to a temp buffer or file.

2. **Read its dimensions**

   ```ts
   import sharp from 'sharp';
   const buffer = await file.arrayBuffer();           // or fs.readFile if on disk
   const { width, height } = await sharp(Buffer.from(buffer)).metadata();
   ```

3. **Choose the closest GPT-Image-1 size**

   ```ts
   const ratio = width! / height!;
   let sizeParam: '1024x1024' | '1536x1024' | '1024x1536';
   if (ratio > 1.2)       sizeParam = '1536x1024';
   else if (ratio < 0.8)  sizeParam = '1024x1536';
   else                   sizeParam = '1024x1024';
   ```

4. **Initialize the OpenAI client**

   ```ts
   import OpenAI from 'openai';
   const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
   ```

5. **Call the `edits` endpoint**

   ```ts
   const response = await openai.images.edits({
     model: 'gpt-image-1',
     image: fs.createReadStream(tempFilePath),  // or Buffer stream
     prompt: userPrompt,
     n: 1,
     size: sizeParam,
     quality: 'high',
     background: 'auto',
     moderation: 'auto',
     output_format: 'png',
   });
   ```

6. **Handle the result**

   * Decode the returned image (`response.data[0].b64_json`) or process the buffer.
   * Upload the final output to Supabase Storage or return it directly.

7. **Clean up**

   * Delete any temp files.
   * Return a JSON response with the new image URL.
