// Minimal test setup
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Simple test component
const TestComponent = () => (
  <div data-testid="test-component">
    <h1>Test Component</h1>
  </div>
);

// Mock the UnifiedImageCard component
const MockUnifiedImageCard = ({ image }: { image: any }) => (
  <div data-testid="mock-unified-image-card">
    <div data-testid="mock-image-url">{image?.image_url}</div>
    <button>Like</button>
    <button>Share</button>
  </div>
);

// Mock the UnifiedImageCard component using the alias so Jest picks up the manual mock
jest.mock('@/components/gallery/unified-image-card');

// Import the mocked component after mocking
import { UnifiedImageCard } from '@/components/gallery/unified-image-card';

describe('Basic Integration Test', () => {
  it('should render a basic component', () => {
    render(<TestComponent />);
    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  it('should pass a simple assertion', () => {
    expect(1 + 1).toBe(2);
  });

  it('should render the mocked UnifiedImageCard', () => {
    const mockImage = {
      id: 'img1',
      user_id: 'test-user',
      style: 'adventure-time',
      style_id: 'adventure-time',
      image_url: '/test.jpg',
      prompt: 'A test image',
      is_shared: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      like_count: 0,
      width: 512,
      height: 512,
      seed: 12345,
      negative_prompt: '',
      cfg_scale: 7.5,
      steps: 30,
      model: 'test-model',
      sampler: 'test-sampler',
      scheduler: 'test-scheduler',
    };

    console.log('UnifiedImageCard at runtime:', UnifiedImageCard);

    render(
      <div data-testid="test-container">
        <UnifiedImageCard image={mockImage} />
      </div>
    );
    
    // Check if the mocked component is rendered
    const mockElement = screen.getByTestId('mock-unified-image-card');
    expect(mockElement).toBeInTheDocument();
    
    // Check if the image URL is displayed
    const imageUrlElement = screen.getByTestId('mock-image-url');
    expect(imageUrlElement).toHaveTextContent('/test.jpg');
  });
});