import { Users } from "lucide-react";

export function CommunityHeader({ imagesCount }: { imagesCount: number }) {
  return (
    <div className="page-transition mb-2">
      <div className="flex items-center gap-2 sm:gap-3">
        {/* Community Icon */}
        <div className="flex size-8 items-center justify-center sm:size-10">
          <Users className="size-5 text-emerald-400 sm:size-6" />
        </div>
        
        {/* Title and Count */}
        <div className="flex flex-col">
          <h1 className="gradient-text text-2xl font-bold leading-tight sm:text-3xl">
            Community Gallery
          </h1>
          <p className="text-xs text-gray-400 sm:text-sm">
            {imagesCount} shared artworks
          </p>
        </div>
      </div>
    </div>
  );
}
