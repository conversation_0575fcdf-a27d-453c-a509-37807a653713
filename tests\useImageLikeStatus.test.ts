// Jest test file for useImageLikeStatus hook
// Requires: @testing-library/react, @testing-library/jest-dom, @types/jest
// Run: npm install --save-dev @testing-library/react @testing-library/jest-dom @types/jest

import { renderHook, waitFor } from "@testing-library/react";
import { useImageLikeStatus } from "../lib/hooks/useImageLikeStatus";
import swr from "swr";
import { supabase } from "../lib/supabase";

jest.mock("swr", () => jest.fn());

jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
  },
}));

jest.mock("../lib/supabase", () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
    },
  },
}));

// Mock fetch globally
global.fetch = jest.fn();

describe("useImageLikeStatus", () => {
  const mockImageId = "test-image-123";
  const mockInitialLikeCount = 5;

  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for swr to prevent destructuring errors
    (swr as jest.Mock).mockReturnValue({
      data: undefined,
      error: undefined,
      mutate: jest.fn(),
      isLoading: false,
    });

    // Default mock for supabase auth
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: { access_token: "mock-token" } },
    });

    // Default mock for fetch
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ like_count: 10, user_liked: true }),
    });
  });

  describe("Return Values", () => {
    it("returns correct structure with default values", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current).toEqual({
        likeCount: mockInitialLikeCount,
        userLiked: false,
        isLoadingLikeStatus: false,
        errorLikeStatus: undefined,
        mutateLikeStatus: expect.any(Function),
      });
    });

    it("returns data from SWR when available", () => {
      const mockData = { like_count: 15, user_liked: false };
      (swr as jest.Mock).mockReturnValue({
        data: mockData,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current).toEqual({
        likeCount: 15,
        userLiked: false,
        isLoadingLikeStatus: false,
        errorLikeStatus: undefined,
        mutateLikeStatus: expect.any(Function),
      });
    });

    it("returns loading state when SWR is loading", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: true,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.isLoadingLikeStatus).toBe(true);
    });

    it("returns error state when SWR has error", () => {
      const mockError = new Error("Network error");
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: mockError,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.errorLikeStatus).toBe(mockError);
    });

    it("returns zero like count when no data and no initial count", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() => useImageLikeStatus(mockImageId));

      expect(result.current.likeCount).toBe(0);
    });

    it("returns false userLiked when no data available", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.userLiked).toBe(false);
    });
  });

  describe("SWR Configuration", () => {
    it("calls SWR with correct key when imageId is provided", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      renderHook(() => useImageLikeStatus(mockImageId));

      expect(swr).toHaveBeenCalledWith(
        `/api/images/${mockImageId}/likes`,
        expect.any(Function),
        expect.objectContaining({
          revalidateOnFocus: true,
        }),
      );
    });

    it("calls SWR with null key when imageId is empty", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      renderHook(() => useImageLikeStatus(""));

      expect(swr).toHaveBeenCalledWith(
        null,
        expect.any(Function),
        expect.any(Object),
      );
    });

    it("calls SWR with null key when imageId is null", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      renderHook(() => useImageLikeStatus(null as any));

      expect(swr).toHaveBeenCalledWith(
        null,
        expect.any(Function),
        expect.any(Object),
      );
    });
  });

  describe("Fetcher Function", () => {
    it("includes authorization header when session is available", async () => {
      const mockSession = { access_token: "test-token" };
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: mockSession },
      });

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ like_count: 10, user_liked: true }),
      });

      // First render the hook to trigger SWR call
      renderHook(() => useImageLikeStatus(mockImageId));

      // Get the fetcher function from the SWR call
      const fetcherFunction = (swr as jest.Mock).mock.calls[0][1];

      await fetcherFunction("/api/images/test/likes");

      expect(global.fetch).toHaveBeenCalledWith("/api/images/test/likes", {
        headers: {
          Authorization: "Bearer test-token",
        },
        method: 'GET',
      });
    });

    it("does not include authorization header when no session", async () => {
      (supabase.auth.getSession as jest.Mock).mockResolvedValue({
        data: { session: null },
      });

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ like_count: 10, user_liked: true }),
      });

      // First render the hook to trigger SWR call
      renderHook(() => useImageLikeStatus(mockImageId));

      const fetcherFunction = (swr as jest.Mock).mock.calls[0][1];

      await fetcherFunction("/api/images/test/likes");

      expect(global.fetch).toHaveBeenCalledWith("/api/images/test/likes", {
        headers: {},
        method: 'GET',
      });
    });

    it("throws error when response is not ok", async () => {
      const mockErrorResponse = {
        ok: false,
        statusText: "Not Found",
        json: () => Promise.resolve({ error: "Image not found" }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockErrorResponse);

      // First render the hook to trigger SWR call
      renderHook(() => useImageLikeStatus(mockImageId));

      const fetcherFunction = (swr as jest.Mock).mock.calls[0][1];

      await expect(fetcherFunction("/api/images/test/likes")).rejects.toThrow(
        "Image not found",
      );
    });

    it("throws error with status text when json parsing fails", async () => {
      const mockErrorResponse = {
        ok: false,
        statusText: "Internal Server Error",
        json: () => Promise.reject(new Error("JSON parse error")),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockErrorResponse);

      // First render the hook to trigger SWR call
      renderHook(() => useImageLikeStatus(mockImageId));

      const fetcherFunction = (swr as jest.Mock).mock.calls[0][1];

      await expect(fetcherFunction("/api/images/test/likes")).rejects.toThrow(
        "JSON parse error",
      );
    });

    it("returns parsed JSON when response is successful", async () => {
      const mockData = { like_count: 20, user_liked: false };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      // First render the hook to trigger SWR call
      renderHook(() => useImageLikeStatus(mockImageId));

      const fetcherFunction = (swr as jest.Mock).mock.calls[0][1];

      const result = await fetcherFunction("/api/images/test/likes");

      expect(result).toEqual(mockData);
    });
  });

  describe("Edge Conditions", () => {
    it("handles undefined initialLikeCount", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, undefined),
      );

      expect(result.current.likeCount).toBe(0);
    });

    it("handles null initialLikeCount", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, null as any),
      );

      expect(result.current.likeCount).toBe(0);
    });

    it("handles zero initialLikeCount", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() => useImageLikeStatus(mockImageId, 0));

      expect(result.current.likeCount).toBe(0);
    });

    it("handles negative initialLikeCount", () => {
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() => useImageLikeStatus(mockImageId, -5));

      expect(result.current.likeCount).toBe(-5);
    });

    it("handles data with null like_count", () => {
      (swr as jest.Mock).mockReturnValue({
        data: { like_count: null, user_liked: true },
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.likeCount).toBe(mockInitialLikeCount);
    });

    it("handles data with undefined like_count", () => {
      (swr as jest.Mock).mockReturnValue({
        data: { like_count: undefined, user_liked: true },
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.likeCount).toBe(mockInitialLikeCount);
    });

    it("handles data with null user_liked", () => {
      (swr as jest.Mock).mockReturnValue({
        data: { like_count: 10, user_liked: null },
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.userLiked).toBe(false);
    });

    it("handles data with undefined user_liked", () => {
      (swr as jest.Mock).mockReturnValue({
        data: { like_count: 10, user_liked: undefined },
        error: undefined,
        mutate: jest.fn(),
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.userLiked).toBe(false);
    });
  });

  describe("Mutate Function", () => {
    it("returns mutate function from SWR", () => {
      const mockMutate = jest.fn();
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      expect(result.current.mutateLikeStatus).toBe(mockMutate);
    });

    it("allows calling mutate function", () => {
      const mockMutate = jest.fn();
      (swr as jest.Mock).mockReturnValue({
        data: undefined,
        error: undefined,
        mutate: mockMutate,
        isLoading: false,
      });

      const { result } = renderHook(() =>
        useImageLikeStatus(mockImageId, mockInitialLikeCount),
      );

      result.current.mutateLikeStatus();

      expect(mockMutate).toHaveBeenCalledTimes(1);
    });
  });
});
