import React from "react";
import { cn } from "@/lib/utils";

interface UnifiedLoaderProps {
  text?: string;
  className?: string;
}

const UnifiedLoader: React.FC<UnifiedLoaderProps> = ({
  text = "Loading...",
  className = "",
}) => (
  <div
    className={cn(
      "flex flex-col items-center justify-center w-full h-full py-8",
      className
    )}
    role="status"
    aria-live="polite"
  >
    <div
      className="relative mb-3 size-12"
      style={{ filter: "drop-shadow(0 0 24px rgba(16, 185, 129, 0.4))" }}
    >
      <div className="animate-spinner-spin bg-spinner-gradient absolute inset-0 rounded-full"></div>
      <div className="absolute inset-1 rounded-full bg-background"></div>
    </div>

    <span className="select-none text-base font-medium text-muted-foreground drop-shadow-lg">
      {text}
    </span>
  </div>
);

export default UnifiedLoader;
