import {
  OpenAIService,
  OpenAIImageResponse,
  IMAGE_GENERATION_CONFIG,
} from "@/lib/openai-service";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";

// Define Zod schema for request body validation
const generateImageSchema = z.object({
  styleId: z.string().min(1, { message: "Style ID is required" }),
  customPrompt: z
    .string()
    .max(1000, { message: "Custom prompt too long" })
    .optional(),
});

// Helper function to detect image MIME type from buffer
function detectImageMimeType(buffer: Buffer): string {
  if (
    buffer.length >= 8 &&
    buffer[0] === 0x89 &&
    buffer[1] === 0x50 &&
    buffer[2] === 0x4e &&
    buffer[3] === 0x47
  ) {
    return "image/png";
  }
  if (
    buffer.length >= 3 &&
    buffer[0] === 0xff &&
    buffer[1] === 0xd8 &&
    buffer[2] === 0xff
  ) {
    return "image/jpeg";
  }
  if (
    buffer.length >= 12 &&
    buffer[0] === 0x52 &&
    buffer[1] === 0x49 &&
    buffer[2] === 0x46 &&
    buffer[3] === 0x46 &&
    buffer[8] === 0x57 &&
    buffer[9] === 0x45 &&
    buffer[10] === 0x42 &&
    buffer[11] === 0x50
  ) {
    return "image/webp";
  }
  if (
    buffer.length >= 6 &&
    buffer[0] === 0x47 &&
    buffer[1] === 0x49 &&
    buffer[2] === 0x46
  ) {
    return "image/gif";
  }
  return "image/png";
}

export async function generateImageHandler({
  formData,
  headers: requestHeaders,
}: {
  formData: FormData;
  headers: Headers;
}): Promise<{ status: number; body: any }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceKey) {
      return {
        status: 500,
        body: {
          error: "Server configuration error: Missing Supabase credentials",
        },
      };
    }
    if (!OpenAIService.isConfigured()) {
      return {
        status: 500,
        body: {
          error: "Server configuration error: OpenAI API key not configured",
        },
      };
    }
    const styleId = formData.get("styleId") as string;
    const customPrompt = formData.get("customPrompt") as string | null;
    const uploadedImage = formData.get("uploadedImage") as File | null;
    if (!styleId && !uploadedImage) {
      return {
        status: 400,
        body: {
          error: "Please select a style and upload an image before generating.",
        },
      };
    }
    if (!styleId) {
      return {
        status: 400,
        body: { error: "Please select a style before generating an image." },
      };
    }
    if (!uploadedImage) {
      return {
        status: 400,
        body: { error: "Please upload an image before generating." },
      };
    }
    const validationResult = generateImageSchema.safeParse({
      styleId,
      customPrompt: customPrompt || undefined,
    });
    if (!validationResult.success) {
      return {
        status: 400,
        body: {
          error: "Invalid request data",
          details: validationResult.error.flatten(),
        },
      };
    }
    const authHeader = requestHeaders.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { status: 401, body: { error: "Authentication required" } };
    }
    const token = authHeader.replace("Bearer ", "");
    const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
      auth: { autoRefreshToken: false, persistSession: false },
    });
    const {
      data: { user },
      error: authError,
    } = await supabaseServer.auth.getUser(token);
    if (authError || !user) {
      return {
        status: 401,
        body: { error: "Invalid or expired authentication token" },
      };
    }
    const basePrompt = OpenAIService.buildPrompt(styleId);
    let imageResponse: OpenAIImageResponse;
    try {
      imageResponse = await OpenAIService.generateImage(
        styleId,
        uploadedImage,
        customPrompt || undefined,
      );
    } catch (openaiError: any) {
      const errorMsg = openaiError?.error?.message || openaiError.message;
      if (
        openaiError.code === "moderation_blocked" ||
        openaiError.type === "image_generation_user_error" ||
        (openaiError.error &&
          typeof openaiError.error === "object" &&
          openaiError.error.code === "moderation_blocked") ||
        (typeof openaiError.message === "string" &&
          openaiError.message.toLowerCase().includes("safety system"))
      ) {
        return {
          status: 400,
          body: {
            error:
              "OpenAI Safety System: Your image was blocked by OpenAI's moderation filters. This can happen even with safe images. Please try again with a different image.",
          },
        };
      }
      if (openaiError.status === 429) {
        return {
          status: 429,
          body: {
            error: "Rate limit exceeded. Please try again in a few moments.",
          },
        };
      }
      if (openaiError.status === 401) {
        return {
          status: 500,
          body: {
            error:
              "OpenAI API authentication failed. Please check API key configuration.",
          },
        };
      }
      if (openaiError.status === 400) {
        return {
          status: 400,
          body: {
            error: "Invalid prompt or request. Please try a different prompt.",
          },
        };
      }
      if (
        openaiError.message?.includes("fetch failed") ||
        openaiError.message?.includes("SocketError") ||
        openaiError.message?.includes("other side closed") ||
        openaiError.code === "UND_ERR_SOCKET"
      ) {
        return {
          status: 503,
          body: {
            error:
              "Network connectivity issue. This may be due to environment restrictions. Please try again or contact support.",
          },
        };
      }
      if (
        openaiError.message?.includes("timeout") ||
        openaiError.code === "ETIMEDOUT"
      ) {
        return {
          status: 408,
          body: {
            error: "Request timed out. Please try again with a shorter prompt.",
          },
        };
      }
      return {
        status: 500,
        body: {
          error: `Image generation failed: ${errorMsg || "Unknown error"}`,
        },
      };
    }
    let imageBuffer: Buffer | undefined = undefined;
    let contentType: string = "image/png";
    if (imageResponse.url) {
      let retryCount = 0;
      const maxRetries = 3;
      while (retryCount < maxRetries) {
        try {
          const imageDownloadResponse = await fetch(imageResponse.url, {
            method: "GET",
            headers: { "User-Agent": "PxlMorphAI/1.0" },
            signal: AbortSignal.timeout(30000),
          });
          if (!imageDownloadResponse.ok) {
            throw new Error(
              `Failed to download image: ${imageDownloadResponse.status} ${imageDownloadResponse.statusText}`,
            );
          }
          const imageBlob = await imageDownloadResponse.arrayBuffer();
          imageBuffer = Buffer.from(imageBlob);
          contentType = detectImageMimeType(imageBuffer);
          break;
        } catch (_downloadError: any) {
          retryCount++;
          if (retryCount >= maxRetries) {
            const { data: savedImage, error: dbError } = await supabaseServer
              .from("images")
              .insert([
                {
                  user_id: user.id,
                  image_url: imageResponse.url,
                  style: styleId,
                  is_shared: false,
                },
              ])
              .select()
              .single();
            if (dbError) {
              return {
                status: 500,
                body: { error: "Failed to save image metadata to database" },
              };
            }
            return {
              status: 200,
              body: {
                imageData: savedImage,
                message:
                  "Image generated successfully (using direct URL due to download limitations)",
                warning: "Image is stored as external URL and may expire",
              },
            };
          }
          await new Promise((resolve) =>
            setTimeout(resolve, Math.pow(2, retryCount) * 1000),
          );
        }
      }
    } else if (imageResponse.b64_json) {
      try {
        imageBuffer = Buffer.from(imageResponse.b64_json, "base64");
        contentType = detectImageMimeType(imageBuffer);
      } catch (_base64Error: any) {
        return {
          status: 500,
          body: { error: "Failed to process image data from OpenAI" },
        };
      }
    } else {
      return {
        status: 500,
        body: { error: "No valid image data received from OpenAI" },
      };
    }
    const { data: buckets, error: listError } =
      await supabaseServer.storage.listBuckets();
    if (!listError) {
      const bucketExists = buckets?.some(
        (bucket) => bucket.name === "private-images",
      );
      if (!bucketExists) {
        const { error: createError } =
          await supabaseServer.storage.createBucket("private-images", {
            public: false,
            allowedMimeTypes: [
              "image/png",
              "image/jpeg",
              "image/gif",
              "image/webp",
            ],
            fileSizeLimit: 10 * 1024 * 1024,
          });
      }
    }
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileExtension =
      contentType === "image/jpeg"
        ? "jpg"
        : contentType === "image/webp"
          ? "webp"
          : contentType === "image/gif"
            ? "gif"
            : "png";
    const fileName = `${timestamp}-${styleId}.${fileExtension}`;
    const filePath = `${user.id}/${fileName}`;
    if (!imageBuffer) {
      throw new Error("imageBuffer is undefined after image processing");
    }
    const { data: uploadData, error: uploadError } =
      await supabaseServer.storage
        .from("private-images")
        .upload(filePath, imageBuffer, {
          contentType: contentType,
          upsert: false,
          cacheControl: "3600",
        });
    if (uploadError) {
      if (imageResponse.url) {
        const { data: savedImage, error: dbError } = await supabaseServer
          .from("images")
          .insert([
            {
              user_id: user.id,
              image_url: imageResponse.url,
              style: styleId,
              is_shared: false,
            },
          ])
          .select()
          .single();
        if (dbError) {
          return {
            status: 500,
            body: { error: "Failed to save image metadata to database" },
          };
        }
        return {
          status: 200,
          body: {
            imageData: savedImage,
            message:
              "Image generated successfully (using direct URL due to storage limitations)",
            warning: "Image is stored as external URL and may expire",
          },
        };
      } else {
        return {
          status: 500,
          body: {
            error:
              "Failed to upload image to storage and no URL fallback available",
          },
        };
      }
    }
    const storedImagePath = `private-images/${filePath}`;
    const { data: savedImage, error: dbError } = await supabaseServer
      .from("images")
      .insert([
        {
          user_id: user.id,
          image_url: storedImagePath,
          style: styleId,
          is_shared: false,
        },
      ])
      .select()
      .single();
    if (dbError) {
      return {
        status: 500,
        body: {
          error: "Failed to save image metadata to database",
          details: dbError,
        },
      };
    }
    return {
      status: 200,
      body: {
        message: "Image generated and saved successfully",
        imageData: savedImage, // Return full image object
      },
    };
  } catch (error: any) {
    if (error instanceof Error) {
      if (
        error.message.includes("requestAsyncStorage") ||
        error.message.includes("headers not available")
      ) {
        return {
          status: 500,
          body: {
            error:
              "Server context error. The request was not processed in the correct context.",
          },
        };
      }
      if (error.message.includes("insufficient_quota")) {
        return {
          status: 429,
          body: {
            error: "OpenAI API quota exceeded. Please check your billing.",
          },
        };
      }
      if (error.message.includes("rate_limit_exceeded")) {
        return {
          status: 429,
          body: { error: "Rate limit exceeded. Please try again later." },
        };
      }
      if (
        error.message.includes("fetch failed") ||
        error.message.includes("SocketError") ||
        error.message.includes("other side closed")
      ) {
        return {
          status: 503,
          body: {
            error:
              "Network connectivity issue detected. This may be due to environment restrictions. Please try again or use a different network.",
          },
        };
      }
    }
    return {
      status: 500,
      body: {
        error:
          error instanceof Error ? error.message : "Failed to generate image",
      },
    };
  }
}
