"use client";
export const dynamic = "force-dynamic";
// Gallery Page (protected)
// Shows user's generated images. Requires authentication.

import { Suspense } from "react";
import { GalleryHeader } from "./components/GalleryHeader";
import { GalleryFilters } from "./components/GalleryFilters";
import { GalleryGrid } from "./components/GalleryGrid";
import { useGalleryPageContext, GalleryPageProvider } from "./components/GalleryPageContext";
import { GeneratedImage } from "@/lib/types";
import { useGeneration } from "@/lib/contexts/GenerationContext";

declare global {
  interface Window {
    __GALLERY_DEBUG__?: {
      userLoading: boolean;
      isLoading: boolean;
      imagesLength: number;
      filteredImagesLength: number;
    };
  }
}

function GalleryPageInner() {
  const {
    images,
    filteredImages,
    isLoading,
    error,
    handleImageShare,
    handleImageDelete,
    styles,
    styleFilter,
    shareFilter,
    handleStyleFilterChange,
    handleShareFilterChange,
    loadMore,
    hasMore,
  } = useGalleryPageContext();
  const { isGenerating, lastGeneratedImage } = useGeneration();

  // Only show placeholder if generating and the new image is not present
  const shouldShowPlaceholder = isGenerating && (!lastGeneratedImage || !images.some(img => img.id === lastGeneratedImage.id));
  type GalleryImage = typeof filteredImages[number] & { generating?: boolean };
  const galleryImages: GalleryImage[] = shouldShowPlaceholder ? [{ id: 'generating', generating: true } as GalleryImage, ...filteredImages] : filteredImages;

  // Debug overlay
  if (typeof window !== 'undefined') {
    window.__GALLERY_DEBUG__ = {
      userLoading: false, // userLoading is removed
      isLoading,
      imagesLength: images.length,
      filteredImagesLength: filteredImages.length,
    };
  }

  // Always render the grid, never show a full-page loader
  return (
    <div className="flex w-full min-w-0 flex-col" aria-label="Gallery main content">
      <section>
        <GalleryHeader
          imagesCount={images.length}
          sharedCount={images.filter((img) => img.is_shared).length}
        />
      </section>
      <section>
        <GalleryFilters
          styleFilter={styleFilter}
          setStyleFilter={handleStyleFilterChange}
          shareFilter={shareFilter}
          setShareFilter={handleShareFilterChange}
          styles={styles || []}
        />
      </section>
      <section className="flex flex-1 flex-col">
        <GalleryGrid
          filteredImages={galleryImages as any}
          onShare={handleImageShare}
          onDelete={handleImageDelete}
          isLoading={isLoading}
          error={error ? (typeof error === "string" ? error : error.message) : null}
          loadMore={loadMore}
          hasMore={hasMore}
        />
      </section>
    </div>
  );
}

export default function GalleryPage() {
  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        <GalleryPageProvider>
          <GalleryPageInner />
        </GalleryPageProvider>
      </Suspense>
    </>
  );
}
