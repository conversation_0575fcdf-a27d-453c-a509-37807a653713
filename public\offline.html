<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StyleGen AI - Offline</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(
          135deg,
          #0f172a 0%,
          #1e293b 50%,
          #0f172a 100%
        );
        color: #f1f5f9;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .offline-container {
        text-align: center;
        max-width: 500px;
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(139, 92, 246, 0.2);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      .offline-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #8b5cf6, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
        color: #cbd5e1;
      }

      .retry-button {
        background: linear-gradient(135deg, #8b5cf6, #3b82f6);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
      }

      .retry-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
      }

      .retry-button:active {
        transform: translateY(0);
      }

      .features {
        margin-top: 30px;
        text-align: left;
      }

      .features h3 {
        color: #8b5cf6;
        margin-bottom: 15px;
        font-size: 1.2rem;
      }

      .features ul {
        list-style: none;
        color: #cbd5e1;
      }

      .features li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
      }

      .features li::before {
        content: "✨";
        position: absolute;
        left: 0;
        color: #8b5cf6;
      }

      .connection-status {
        margin-top: 20px;
        padding: 10px;
        border-radius: 8px;
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #fca5a5;
        font-size: 0.9rem;
      }

      @media (max-width: 480px) {
        .offline-container {
          padding: 30px 20px;
        }

        h1 {
          font-size: 1.5rem;
        }

        p {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="offline-container">
      <div class="offline-icon">📡</div>
      <h1>You're Offline</h1>
      <p>
        It looks like you've lost your internet connection. Don't worry - you
        can still browse your previously generated images!
      </p>

      <button class="retry-button" onclick="window.location.reload()">
        Try Again
      </button>

      <div class="features">
        <h3>What you can do offline:</h3>
        <ul>
          <li>View your saved images in the gallery</li>
          <li>Browse style examples</li>
          <li>Access your favorites</li>
          <li>Review your recent creations</li>
        </ul>
      </div>

      <div class="connection-status">
        <strong>Status:</strong> No internet connection detected
      </div>
    </div>

    <script>
      // Check connection status
      function updateConnectionStatus() {
        const statusElement = document.querySelector(".connection-status");
        if (navigator.onLine) {
          statusElement.innerHTML =
            '<strong>Status:</strong> Connection restored! <a href="/" style="color: #8b5cf6;">Go to app</a>';
          statusElement.style.background = "rgba(34, 197, 94, 0.1)";
          statusElement.style.borderColor = "rgba(34, 197, 94, 0.3)";
          statusElement.style.color = "#86efac";
        } else {
          statusElement.innerHTML =
            "<strong>Status:</strong> No internet connection detected";
          statusElement.style.background = "rgba(239, 68, 68, 0.1)";
          statusElement.style.borderColor = "rgba(239, 68, 68, 0.3)";
          statusElement.style.color = "#fca5a5";
        }
      }

      // Listen for online/offline events
      window.addEventListener("online", updateConnectionStatus);
      window.addEventListener("offline", updateConnectionStatus);

      // Initial check
      updateConnectionStatus();

      // Auto-retry when connection is restored
      window.addEventListener("online", () => {
        setTimeout(() => {
          if (navigator.onLine) {
            window.location.reload();
          }
        }, 2000);
      });
    </script>
  </body>
</html>
