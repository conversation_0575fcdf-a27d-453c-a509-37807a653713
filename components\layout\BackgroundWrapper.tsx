import React from "react";

interface BackgroundWrapperProps {
  children?: React.ReactNode;
}

// AppBackground: Fixed background gradient for the app
const AppBackground: React.FC<BackgroundWrapperProps> = ({ children }) => {
  return (
    <div className="fixed inset-0 z-[-1] size-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {children}
    </div>
  );
};

export default AppBackground; 