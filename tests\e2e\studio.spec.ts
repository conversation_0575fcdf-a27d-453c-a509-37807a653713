import { test, expect } from "@playwright/test";

test.describe("Studio Page", () => {
  test("should render studio hero or login form", async ({ page }) => {
    await page.goto("/studio");
    const studioHeader = page.getByRole("heading", { name: /Studio/i });
    const signInTab = page.getByRole("tab", { name: /Sign In/i });
    if (await studioHeader.isVisible({ timeout: 2000 })) {
      await expect(studioHeader).toBeVisible();
    } else {
      await expect(signInTab).toBeVisible();
    }
  });

  test("should have correct SEO meta tags", async ({ page }) => {
    await page.goto("/studio");
    await expect(page).toHaveTitle("PxlMorph AI - AI-Powered Image Generation");
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      "content",
      /Create stunning AI-generated images with advanced style controls and real-time editing capabilities\./i
    );
  });
});
