import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const mkdir = promisify(fs.mkdir);

// Configuration
const SOURCE_DIR = path.join(__dirname, '../public/style-examples');
const DEST_DIR = path.join(__dirname, '../public/optimized-style-examples');
const QUALITY = 80; // Quality for JPEG/WebP (1-100)
const WIDTH = 800; // Max width in pixels
const CONCURRENCY = 4; // Number of images to process in parallel

// Ensure output directory exists
async function ensureDir(dir) {
  try {
    await mkdir(dir, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') throw err;
  }
}

// Process a single image
async function processImage(filePath, destPath) {
  try {
    const image = sharp(filePath);
    const metadata = await image.metadata();
    
    // Only process if the destination doesn't exist or source is newer
    try {
      const destStat = await stat(destPath);
      const srcStat = await stat(filePath);
      if (destStat.mtime > srcStat.mtime) {
        console.log(`Skipping ${filePath} - already optimized`);
        return;
      }
    } catch (err) {
      // Destination doesn't exist, continue with processing
    }

    console.log(`Processing ${filePath}...`);
    
    // Resize and convert to WebP
    await image
      .resize({
        width: WIDTH,
        height: metadata.height && (metadata.height > metadata.width) ? Math.round(WIDTH * (metadata.height / metadata.width)) : null,
        fit: 'inside',
        withoutEnlargement: true,
      })
      .webp({
        quality: QUALITY,
        effort: 6, // Higher effort = better compression but slower
      })
      .toFile(destPath);
    
    console.log(`Optimized ${filePath} -> ${destPath}`);
  } catch (err) {
    console.error(`Error processing ${filePath}:`, err);
  }
}

// Process all images in directory
async function processDirectory() {
  try {
    await ensureDir(DEST_DIR);
    const files = await readdir(SOURCE_DIR);
    const imageFiles = files.filter(file => 
      /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
    );

    console.log(`Found ${imageFiles.length} images to process`);
    
    // Process images with limited concurrency
    const processQueue = [];
    let running = 0;
    
    for (const file of imageFiles) {
      const srcPath = path.join(SOURCE_DIR, file);
      const destPath = path.join(
        DEST_DIR, 
        path.basename(file, path.extname(file)) + '.webp'
      );
      
      // Add to queue
      processQueue.push({ srcPath, destPath });
    }
    
    // Process queue with concurrency control
    const processNext = async () => {
      if (processQueue.length === 0) return;
      if (running >= CONCURRENCY) return;
      
      const { srcPath, destPath } = processQueue.shift();
      running++;
      
      try {
        await processImage(srcPath, destPath);
      } finally {
        running--;
        await processNext();
      }
    };
    
    // Start processing
    const maxConcurrent = Math.min(CONCURRENCY, processQueue.length);
    await Promise.all(Array(maxConcurrent).fill().map(processNext));
    
    console.log('Image optimization complete!');
    
    // Generate a mapping file for easy reference
    const mapping = {};
    for (const file of imageFiles) {
      const originalName = path.basename(file, path.extname(file));
      mapping[originalName] = `optimized-style-examples/${originalName}.webp`;
    }
    
    await fs.promises.writeFile(
      path.join(__dirname, '../public/optimized-style-mapping.json'),
      JSON.stringify(mapping, null, 2)
    );
    
    console.log('Generated optimized image mapping');
    
  } catch (err) {
    console.error('Error during image optimization:', err);
    process.exit(1);
  }
}

// Run the script
processDirectory();
