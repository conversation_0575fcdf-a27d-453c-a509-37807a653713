// Updated image generation configuration to match your exact OpenAI Playground example
export const IMAGE_GENERATION_CONFIG = {
  maxFileSize: 4 * 1024 * 1024, // 4MB for higher quality images
  allowedFileTypes: ["image/png", "image/jpeg", "image/gif"],
  imageSize: "1024x1024", // Matching your playground example
  quality: "medium", // Matching your playground example
};

export const CACHE_KEYS = {
  GENERATED_IMAGES: "pxlmorph_generated_images",
  USER_PROFILE: "pxlmorph_user_profile",
  OFFLINE_STATUS: "pxlmorph_offline_status",
  COMMUNITY_IMAGES: "pxlmorph_community_images",
};
