"use client";

import { useEffect, useState } from "react";

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // This effect can be used to trigger animations after mount
    setMounted(true);
  }, []);

  // Use a more SSR-friendly approach that doesn't cause hydration mismatches
  return (
    <div
      className={`page-transition ${mounted ? "page-transition-active" : ""}`}
    >
      {/*
        Add CSS for .page-transition and .page-transition-active
        to handle opacity or transform for entry animation.
        Example CSS:
        .page-transition { opacity: 0; transition: opacity 0.5s ease-in-out; }
        .page-transition-active { opacity: 1; }
      */}
      {children}
    </div>
  );
}
