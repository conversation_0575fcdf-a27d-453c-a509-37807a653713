import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

// Define GalleryFiltersProps interface for props
interface GalleryFiltersProps {
  styleFilter: string;
  setStyleFilter: (s: string) => void;
  shareFilter: string;
  setShareFilter: (s: string) => void;
  styles: Array<{ id: string; name: string }>;
}

export function GalleryFilters({
  styleFilter,
  setStyleFilter,
  shareFilter,
  setShareFilter,
  styles = [],
}: GalleryFiltersProps) {
  // Defensive: always ensure options are available
  const styleOptions = ["all", ...styles.map((style: { id: string }) => style.id)];
  const safeStyleFilter = styleOptions.includes(styleFilter)
    ? styleFilter
    : "all";
  const shareOptions = ["all", "shared", "private"];
  const safeShareFilter = shareOptions.includes(shareFilter)
    ? shareFilter
    : "all";

  // Get display names for selected values
  const getStyleDisplayName = (filter: string) => {
    if (filter === "all") return "All Styles";
    const style = styles.find((s) => s.id === filter);
    return style ? style.name : "All Styles";
  };

  const getShareDisplayName = (filter: string) => {
    switch (filter) {
      case "all":
        return "All Images";
      case "shared":
        return "Shared Only";
      case "private":
        return "Private Only";
      default:
        return "All Images";
    }
  };

  // Handle filter changes with smooth transitions
  const handleStyleChange = (value: string) => {
    if (value && value !== safeStyleFilter) {
      setStyleFilter(value);
    }
  };

  const handleShareChange = (value: string) => {
    if (value && value !== safeShareFilter) {
      setShareFilter(value);
    }
  };

  const triggerClasses = "w-full h-8 sm:h-9 bg-slate-800/50 border-slate-700 text-white hover:bg-slate-700/50 transition-colors duration-200 text-xs sm:text-sm";
  const contentClasses = "bg-slate-800 border-slate-700 text-white";
  const itemClasses = "hover:bg-slate-700 focus:bg-slate-700 data-[highlighted]:bg-slate-700 data-[state=checked]:bg-violet-600";

  return (
    <div className="stagger-animation mb-2 flex flex-row flex-wrap gap-2 sm:gap-3">
      {/* Style Filter */}
      <div className="min-w-[140px] max-w-full flex-1 sm:min-w-[160px] sm:max-w-[200px]">
        <Select value={safeStyleFilter} onValueChange={handleStyleChange}>
          <SelectTrigger className={cn(triggerClasses, "data-[state=open]:bg-slate-700/80")}>
            <SelectValue placeholder="All Styles">
              {getStyleDisplayName(safeStyleFilter)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className={contentClasses}>
            <SelectItem value="all" className={itemClasses}>
              All Styles
            </SelectItem>
            {styles.map((style) => (
              <SelectItem
                key={style.id}
                value={style.id}
                className={itemClasses}
              >
                {style.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Share Filter */}
      <div className="min-w-[140px] max-w-full flex-1 sm:min-w-[160px] sm:max-w-[200px]">
        <Select value={safeShareFilter} onValueChange={handleShareChange}>
          <SelectTrigger className={cn(triggerClasses, "data-[state=open]:bg-slate-700/80")}>
            <SelectValue placeholder="All Images">
              {getShareDisplayName(safeShareFilter)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className={contentClasses}>
            <SelectItem value="all" className={itemClasses}>
              All Images
            </SelectItem>
            <SelectItem value="shared" className={itemClasses}>
              Shared Only
            </SelectItem>
            <SelectItem value="private" className={itemClasses}>
              Private Only
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
