const config = {
  preset: "ts-jest/presets/js-with-ts",
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.(ts|tsx|js|jsx)$": [
      "babel-jest",
      { configFile: "./tests/babel.config.js" },
    ],
  },
  testMatch: [
    "**/tests/**/*.(test|spec).[jt]s?(x)",
    "!**/tests/e2e/**",
    "!**/*.spec.ts", // Exclude Playwright E2E specs if not in e2e folder
  ],
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json", "node"],
  transformIgnorePatterns: ["/node_modules/(?!(@supabase|isows)/)"],
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
};
export default config;
