import * as RadixTooltip from "@radix-ui/react-tooltip";
import React from "react";

// Unified tooltip class for consistent styling
const tooltipClass =
  "px-2 py-1 rounded-lg bg-slate-900/70 backdrop-blur-md text-white text-xs font-semibold shadow-xl z-50 border border-slate-700/60";

/**
 * Unified Tooltip components for consistent usage across the app.
 *
 * Usage:
 * import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent, TooltipArrow } from "@/components/ui/tooltip";
 *
 * <TooltipProvider delayDuration={100}>
 *   <Tooltip>
 *     <TooltipTrigger asChild>
 *       <button>Hover me</button>
 *     </TooltipTrigger>
 *     <TooltipContent sideOffset={8}>Tooltip text<TooltipArrow /></TooltipContent>
 *   </Tooltip>
 * </TooltipProvider>
 */

export const TooltipProvider = RadixTooltip.Provider;
export const Tooltip = RadixTooltip.Root;
export const TooltipTrigger = RadixTooltip.Trigger;

// Unified TooltipArrow for consistent style everywhere
export const TooltipArrow = React.forwardRef<
  React.ElementRef<typeof RadixTooltip.Arrow>,
  React.ComponentPropsWithoutRef<typeof RadixTooltip.Arrow>
>(({ className = "", ...props }, ref) => (
  <RadixTooltip.Arrow
    ref={ref}
    className={`fill-slate-900/70 ${className}`.trim()}
    {...props}
  />
));
TooltipArrow.displayName = "TooltipArrow";

// TooltipContent with default styling, allows className override
export const TooltipContent = React.forwardRef<
  React.ElementRef<typeof RadixTooltip.Content>,
  React.ComponentPropsWithoutRef<typeof RadixTooltip.Content>
>(({ className = "", children, ...props }, ref) => (
  <RadixTooltip.Content
    ref={ref}
    sideOffset={8}
    className={`tooltip-enter ${tooltipClass} ${className}`.trim()}
    data-radix-tooltip-content
    {...props}
  >
    {children}
  </RadixTooltip.Content>
));
TooltipContent.displayName = "TooltipContent"; 