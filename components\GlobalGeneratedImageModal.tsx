"use client";
import { useGeneration } from "@/lib/contexts/GenerationContext";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { GeneratedImage } from "@/lib/types";

// Dynamically import the modal to avoid SSR issues and improve performance
const DynamicUnifiedImageModal = dynamic(() => import("@/components/gallery/unified-image-modal").then(mod => mod.UnifiedImageModal), { ssr: false, loading: () => null });

export function GlobalGeneratedImageModal() {
  const { lastGeneratedImage, setLastGeneratedImage } = useGeneration();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Listen for imageCreated event which now carries the full image object
    function handleImageCreated(e: Event) {
      const customEvent = e as CustomEvent;
      if (customEvent?.detail?.image) {
        setLastGeneratedImage(customEvent.detail.image);
      }
    }
    window.addEventListener("imageCreated", handleImageCreated);
    return () => window.removeEventListener("imageCreated", handleImageCreated);
  }, [setLastGeneratedImage]);

  useEffect(() => {
    // Open the modal when a new image is available
    if (lastGeneratedImage) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [lastGeneratedImage]);

  const handleClose = () => {
    setIsOpen(false);
    // Use a timeout to allow the modal to animate out before clearing the image
    setTimeout(() => {
      setLastGeneratedImage(null);
    }, 300);
  };

  if (!lastGeneratedImage) return null;
  return (
    <DynamicUnifiedImageModal
      image={lastGeneratedImage as GeneratedImage} // Ensure correct type
      isOpen={isOpen}
      onClose={handleClose}
      showActions={true}
    />
  );
}
