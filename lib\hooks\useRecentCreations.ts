"use client";

import useS<PERSON> from "swr";
import { SupabaseService } from "@/lib/supabase";
import { CacheService } from "@/lib/cache-service";
import { GeneratedImage } from "@/lib/types";

const fetchRecentCreations = async (userId: string) => {
  if (!userId) return [];
  if (typeof window !== "undefined" && !CacheService.isOnline()) {
    // Offline: fallback to IndexedDB
    return await CacheService.getCachedImages();
  }
  try {
    const images = await SupabaseService.getUserImages(userId, 10, 0);
    // Update cache for offline use
    await CacheService.cacheGeneratedImages(images);
    return images;
  } catch (err) {
    // On error, fallback to cache
    return await CacheService.getCachedImages();
  }
};

export function useRecentCreations(userId: string | undefined) {
  const { data, error, isLoading, mutate } = useSWR<GeneratedImage[]>(
    userId ? ["recent-creations", userId] : null,
    () => fetchRecentCreations(userId!),
  );
  return { recentImages: data || [], isLoading, error, mutate };
}
