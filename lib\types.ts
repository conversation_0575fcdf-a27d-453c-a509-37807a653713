export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
  avatar_url?: string;
}

export interface GeneratedImage {
  id: string;
  user_id: string;
  image_url: string;
  style: string;
  inspiration_image_url?: string;
  is_shared: boolean;
  created_at: string;
  updated_at: string;
  shared_at?: string;
  like_count?: number; // Added for denormalized like counts
  user_liked?: boolean; // Added to indicate if the current user has liked this image
}

// Optional: Define a specific type for the Like record itself if needed elsewhere
export interface Like {
  id: string;
  image_id: string;
  user_id: string;
  created_at: string;
}

export interface Style {
  id: string;
  name: string;
  description: string;
  prompt: string;
  example_image?: string;
  category: string;
}


