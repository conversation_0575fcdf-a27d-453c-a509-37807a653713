"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { GeneratedImage } from "@/lib/types";

export interface GenerationState {
  isGenerating: boolean;
  setIsGenerating: (val: boolean) => void;
  lastGeneratedImage: GeneratedImage | null;
  setLastGeneratedImage: (img: GeneratedImage | null) => void;
}

const GenerationContext = createContext<GenerationState | undefined>(undefined);

export function GenerationProvider({ children }: { children: ReactNode }) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGeneratedImage, setLastGeneratedImage] = useState<GeneratedImage | null>(null);

  // Restore generation state from sessionStorage on mount
  useEffect(() => {
    try {
      const storedState = window.sessionStorage.getItem("generationState");
      if (storedState) {
        const { isGenerating: storedIsGenerating, imageId } = JSON.parse(storedState);
        setIsGenerating(storedIsGenerating);
        // If an image was being generated, we can't restore the full object,
        // but this logic could be extended to fetch it if needed.
        // For now, we just restore the "generating" visual state.
      }
    } catch (e) {
      // Ignore session storage errors
    }
  }, []);

  // Update sessionStorage when generation state changes
  const handleSetIsGenerating = (val: boolean) => {
    setIsGenerating(val);
    try {
      if (val) {
        // When generation starts, store the state
        window.sessionStorage.setItem("generationState", JSON.stringify({ isGenerating: true }));
      } else {
        // When generation ends, clear the state
        window.sessionStorage.removeItem("generationState");
      }
    } catch (e) {
      // Ignore session storage errors
    }
  };
  
  const value: GenerationState = {
    isGenerating,
    setIsGenerating: handleSetIsGenerating,
    lastGeneratedImage,
    setLastGeneratedImage,
  };

  return (
    <GenerationContext.Provider value={value}>
      {children}
    </GenerationContext.Provider>
  );
}

export function useGeneration() {
  const ctx = useContext(GenerationContext);
  if (!ctx) throw new Error("useGeneration must be used within a GenerationProvider");
  return ctx;
}
