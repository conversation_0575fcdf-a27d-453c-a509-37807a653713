import useS<PERSON> from "swr";
import { SupabaseService } from "@/lib/supabase";
import { CacheService } from "@/lib/cache-service";
import { GeneratedImage } from "@/lib/types";
import { useCallback, useState, useEffect } from "react";

const fetchAllUserImages = async (userId: string) => {
  if (!userId) return [];
  if (typeof window !== "undefined" && !CacheService.isOnline()) {
    // Offline: fallback to IndexedDB
    const cached = await CacheService.getCachedImages();
    return cached;
  }
  try {
    const images = await SupabaseService.getAllUserImages(userId);
    // Update cache for offline use
    await CacheService.cacheGeneratedImages(images);
    return images;
  } catch (err) {
    // On error, fallback to cache
    if (process.env.NODE_ENV === "development") {
      console.error("[SWR] Error fetching user images, falling back to cache:", err);
    }
    const cached = await CacheService.getCachedImages();
    return cached;
  }
};

export function useAllUserImages(userId: string | undefined) {
  const [images, setImages] = useState<GeneratedImage[]>([]);

  const { data, error, isLoading, mutate } = useSWR<GeneratedImage[]>(
    userId ? ["all-user-images", userId] : null,
    () => fetchAllUserImages(userId!),
    { revalidateOnFocus: false }
  );
  
  useEffect(() => {
    if (data) {
      setImages(data);
    }
  }, [data]);
  
  // No-op loadMore and hasMore for now as we fetch all images
  const loadMore = useCallback(() => {}, []);
  const hasMore = false;

  return { images, setImages, isLoading, error, mutate, hasMore, loadMore };
}
