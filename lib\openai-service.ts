import OpenAI from "openai";
import { Style } from "./types";
import fs from 'fs';
import path from 'path';

// Initialize OpenAI client only on server side
let openai: OpenAI | null = null;

if (typeof window === "undefined" && process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    // Add timeout and retry configuration
    timeout: 60000, // 60 second timeout
    maxRetries: 3, // Retry failed requests up to 3 times
  });
}

// Image generation configuration - matching your exact OpenAI example
export const IMAGE_GENERATION_CONFIG = {
  model: "gpt-image-1",
  n: 1,
  size: "1024x1024" as const,
  quality: "medium" as const,
  background: "opaque" as const,
  moderation: "low" as const, // Use less restrictive filter to bypass safety system
};

// Type for OpenAI image response
export interface OpenAIImageResponse {
  url?: string;
  b64_json?: string;
}

// Cache for styles loaded from JSON
let stylesCache: Style[] | null = null;

export class OpenAIService {
  static isConfigured(): boolean {
    return !!process.env.OPENAI_API_KEY;
  }

  // Load styles from JSON file (server-side only)
  static loadStylesFromJSON(): Style[] {
    if (stylesCache) {
      return stylesCache;
    }

    try {
      const filePath = path.join(process.cwd(), 'style_prompts.json');
      const file = fs.readFileSync(filePath, 'utf-8');
      const stylePromptsRaw = JSON.parse(file);
      const stylePrompts: Style[] = [];

      interface StyleData {
        id?: string;
        title: string;
        prompt: string;
        example_image?: string;
      }

      // Convert JSON structure to Style array
      Object.entries(stylePromptsRaw).forEach(
        ([category, categoryStyles]: [string, StyleData[]]) => {
          categoryStyles.forEach((styleData: StyleData) => {
            // Use explicit id if present, otherwise generate from title for backward compatibility
            const id = styleData.id
              ? styleData.id
              : styleData.title
                  .toLowerCase()
                  .replace(/[^a-z0-9\s-]/g, "")
                  .replace(/\s+/g, "-")
                  .replace(/-+/g, "-")
                  .trim();
            stylePrompts.push({
              id,
              name: styleData.title,
              description: `Transform this image into ${styleData.title} style.`,
              prompt: styleData.prompt,
              example_image: styleData.example_image || null,
              category,
            });
          });
        },
      );

      stylesCache = stylePrompts;
      return stylePrompts;
    } catch (error) {
      console.error("Error loading styles from JSON:", error);
      // Return empty array if JSON loading fails
      return [];
    }
  }

  static buildPrompt(styleId: string): string {
    // Try to get the style from JSON first
    const jsonStyles = this.loadStylesFromJSON();
    const jsonStyle = jsonStyles.find((s) => s.id === styleId);
    if (jsonStyle && jsonStyle.prompt) {
      return jsonStyle.prompt;
    }
    // If style is not found, throw an error
    console.error(`❌ Style not found for styleId: ${styleId}`);
    throw new Error("Style not found");
  }

  static async generateImage(
    styleId: string,
    uploadedImage: File, // Now mandatory, renamed from inspirationFile
  ): Promise<OpenAIImageResponse> {
    try {
      if (!this.isConfigured()) {
        throw new Error("OpenAI API key not configured");
      }

      if (!openai) {
        throw new Error("OpenAI client not initialized");
      }

      const prompt = this.buildPrompt(styleId);

      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 45000); // 45 second timeout

      try {
        const response = await openai.images.edit(
          {
            image: uploadedImage,
            prompt: prompt,
            model: IMAGE_GENERATION_CONFIG.model,
            n: IMAGE_GENERATION_CONFIG.n,
            size: IMAGE_GENERATION_CONFIG.size,
            quality: IMAGE_GENERATION_CONFIG.quality,
            background: IMAGE_GENERATION_CONFIG.background,
            moderation: IMAGE_GENERATION_CONFIG.moderation, // Use low moderation to bypass safety system
          } as OpenAI.Images.ImageEditParams,
          { signal: controller.signal },
        );

        clearTimeout(timeoutId);

        if (!response.data || response.data.length === 0) {
          console.error("❌ OpenAI API edit response missing data:", response);
          throw new Error("No image data returned from OpenAI edit operation");
        }

        const imageData = response.data[0];
        if (!imageData.url && !imageData.b64_json) {
          console.error(
            "❌ OpenAI API edit response missing both URL and base64 data:",
            imageData,
          );
          throw new Error(
            "No image URL or base64 data returned from OpenAI edit operation",
          );
        }

        console.log("✅ OpenAI image edit operation response received");
        return {
          url: imageData.url,
          b64_json: imageData.b64_json,
        };
      } catch (apiError) {
        // Handle specific API errors
        if ((apiError as Error).name === "AbortError") {
          console.error("⏰ OpenAI API request timed out via AbortController.");
          throw new Error(
            "Request timed out. Please try again with a shorter prompt.",
          );
        }
        // Re-throw the original error for upstream handling
        throw apiError;
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error) {
      const apiError = error as { status?: number };
      if (apiError.status === 401) {
        throw new Error(
          "OpenAI API authentication failed. Please check your API key.",
        );
      }
      if (apiError.status === 429) {
        throw new Error(
          "Rate limit exceeded. Please wait a moment and try again.",
        );
      }
      // For all other errors, re-throw the original error so the backend can inspect it
      throw error;
    }
  }
}
