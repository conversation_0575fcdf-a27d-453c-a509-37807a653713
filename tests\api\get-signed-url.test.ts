// @ts-nocheck
import { Buffer } from "buffer";
process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

const mockCreateClient = () => ({
  auth: {
    getUser: jest.fn(() => ({ data: { user: { id: "user1" } }, error: null })),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    maybeSingle: jest.fn(() => ({
      data: { user_id: "user1", is_shared: true },
      error: null,
    })),
  })),
});

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

import { getSignedUrlHandler } from "../../lib/api/getSignedUrlHandler";

describe("getSignedUrlHandler", () => {
  const env = {
    NEXT_PUBLIC_SUPABASE_URL: "url",
    SUPABASE_SERVICE_ROLE_KEY: "key",
  };
  it("returns 401 if no auth header", async () => {
    const result = await getSignedUrlHandler({
      headers: {},
      body: { filePath: "user1/file.png" },
      env,
    });
    expect(result.status).toBe(401);
    expect(result.body.error).toMatch(/Authentication required/);
  });

  it("returns 400 if filePath is missing", async () => {
    const result = await getSignedUrlHandler({
      headers: { authorization: "Bearer validtoken" },
      body: {},
      env,
    });
    expect(result.status).toBe(400);
    expect(result.body.error).toMatch(/File path is required/);
  });

  // More tests for DB error, forbidden, etc. would require deeper mocking of Supabase client
});
