import { Image } from "lucide-react";

export function GalleryHeader({
  imagesCount,
  sharedCount,
}: {
  imagesCount: number;
  sharedCount: number;
}) {
  return (
    <div className="page-transition mb-2">
      <div className="flex items-center gap-2 sm:gap-3">
        {/* Gallery Icon */}
        <div className="flex size-8 items-center justify-center sm:size-10">
          <Image className="size-5 text-blue-400 sm:size-6" aria-label="Gallery icon" />
        </div>
        
        {/* Title and Count */}
        <div className="flex flex-col">
          <h1 className="gradient-text text-2xl font-bold leading-tight sm:text-3xl">
            My Gallery
          </h1>
          <p className="text-xs text-gray-400 sm:text-sm">
            {imagesCount} images • {sharedCount} shared
          </p>
        </div>
      </div>
    </div>
  );
}
