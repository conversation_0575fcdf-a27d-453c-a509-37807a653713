// /lib/api/getStylesHandler.ts
import fs from 'fs';
import path from 'path';

// Define the expected structure of the JSON data
interface Style {
  id: string;
  title: string;
  prompt: string;
  example_image?: string;
}

interface StyleData {
  [category: string]: Style[];
}

export function getStylesHandler() {
  try {
    const filePath = path.join(process.cwd(), 'style_prompts.json');
    const file = fs.readFileSync(filePath, 'utf-8');
    const data: StyleData = JSON.parse(file);
    return { data };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }
}
