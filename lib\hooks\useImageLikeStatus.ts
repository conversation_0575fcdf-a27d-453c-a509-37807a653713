import useS<PERSON> from "swr";
import { supabase } from "@/lib/supabase"; // Add this import
import { apiService } from "@/lib/api/apiService";
import { errorHandler } from "@/lib/utils/errorHandler";
// We might need GeneratedImage type or a specific type for like status response
// For now, let's assume a simple structure for the response.

// SWR fetcher for like status
const fetchLikeStatus = async (url: string) => {
  // Get the current session/access token
  const {
    data: { session },
  } = await supabase.auth.getSession();
  const headers: HeadersInit = {};
  if (session?.access_token) {
    headers["Authorization"] = `Bearer ${session.access_token}`;
  }
  const { data, error } = await apiService.get<any>(url, headers);
  if (error) {
    errorHandler(error, { userMessage: "Could not load like status" });
    throw new Error(error);
  }
  return data;
};

// Hook to manage like status for an image
export function useImageLikeStatus(imageId: string, initialLikeCount?: number) {
  // Always call useSWR at the top level to comply with React Hooks rules
  const shouldFetch = imageId && imageId !== 'generating';
  const { data, error, mutate, isLoading } = useSWR(
    shouldFetch ? `/api/images/${imageId}/likes` : null,
    fetchLikeStatus,
    {
      revalidateOnFocus: true,
    },
  );

  // If imageId is 'generating', return default values
  if (imageId === 'generating') {
    return {
      likeCount: 0,
      userLiked: false,
      isLoadingLikeStatus: false,
      mutateLikeStatus: () => {},
    };
  }

  // Handle error state, potentially logging or showing a toast
  if (error) {
    // console.error("Error fetching like status:", error.message);
    // toast.error(`Could not load like status: ${error.message}`); // Optional: direct error display
  }

  return {
    likeCount: data?.like_count ?? initialLikeCount ?? 0,
    userLiked: data?.user_liked === true, // always boolean
    isLoadingLikeStatus: isLoading,
    errorLikeStatus: error,
    mutateLikeStatus: mutate,
  };
}
