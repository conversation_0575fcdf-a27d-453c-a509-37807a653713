import { createClient } from "@supabase/supabase-js";
import { Database } from "@/lib/database.types";

export async function imageLikesHandler({
  imageId,
  headers,
}: {
  imageId: string;
  headers: Headers;
}): Promise<{ status: number; body: any }> {
  if (!imageId) {
    return { status: 400, body: { error: "Image ID is required" } };
  }
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl || !supabaseServiceKey) {
    return {
      status: 500,
      body: {
        error: "Server configuration error: Missing Supabase credentials",
      },
    };
  }
  const authHeader = headers.get("authorization");
  let userId: string | null = null;
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.replace("Bearer ", "");
    // Inline initialization per request
    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
    const {
      data: { user },
    } = await supabase.auth.getUser(token);
    if (user) {
      userId = user.id;
    }
  }
  const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
  const { data: imageDetails, error: imageError } = await supabase
    .from("images")
    .select("like_count")
    .eq("id", imageId)
    .single();
  if (imageError || !imageDetails) {
    return {
      status: 500,
      body: {
        error:
          "Failed to fetch like data for image: " +
          (imageError?.message || "Image not found"),
      },
    };
  }
  let userLiked: boolean | null = null;
  if (userId) {
    const { data: likeData, error: likeError } = await supabase
      .from("likes")
      .select("id")
      .eq("image_id", imageId)
      .eq("user_id", userId)
      .maybeSingle();
    if (likeError && likeError.code !== "PGRST116") {
      userLiked = false;
    } else {
      userLiked = !!likeData;
    }
  } else {
    userLiked = null;
  }
  return {
    status: 200,
    body: { like_count: imageDetails.like_count, user_liked: userLiked },
  };
}
