import { createClient } from "@supabase/supabase-js";

export async function getUserFavoritesHandler({
  headers: requestHeaders,
}: {
  headers: Headers;
}): Promise<{ status: number; body: any }> {
  try {
    const authHeader = requestHeaders.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { status: 401, body: { error: "Unauthorized" } };
    }
    const token = authHeader.substring(7);
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceKey) {
      return { status: 500, body: { error: "Server configuration error" } };
    }
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return { status: 401, body: { error: "Invalid token" } };
    }
    const { data: favorites, error: favoritesError } = await supabase
      .from("user_favorites")
      .select("style_id")
      .eq("user_id", user.id);
    if (favoritesError) {
      return { status: 500, body: { error: "Failed to fetch favorites" } };
    }
    const styleIds = favorites?.map((fav) => String(fav.style_id)) || [];
    return {
      status: 200,
      body: { favorites: styleIds, count: styleIds.length },
    };
  } catch (error) {
    return { status: 500, body: { error: "Internal server error" } };
  }
}

export async function postUserFavoritesHandler({
  headers: requestHeaders,
  styleId,
  action,
}: {
  headers: Headers;
  styleId: string;
  action: string;
}): Promise<{ status: number; body: any }> {
  try {
    const authHeader = requestHeaders.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { status: 401, body: { error: "Unauthorized" } };
    }
    const token = authHeader.substring(7);
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceKey) {
      return { status: 500, body: { error: "Server configuration error" } };
    }
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return { status: 401, body: { error: "Invalid token" } };
    }
    if (!styleId || !action) {
      return { status: 400, body: { error: "Missing styleId or action" } };
    }
    if (action === "add") {
      const { error: insertError } = await supabase
        .from("user_favorites")
        .insert({ user_id: user.id, style_id: styleId });
      if (insertError) {
        return { status: 500, body: { error: "Failed to add favorite" } };
      }
    } else if (action === "remove") {
      const { error: deleteError } = await supabase
        .from("user_favorites")
        .delete()
        .eq("user_id", user.id)
        .eq("style_id", styleId);
      if (deleteError) {
        return { status: 500, body: { error: "Failed to remove favorite" } };
      }
    } else {
      return { status: 400, body: { error: "Invalid action" } };
    }
    return { status: 200, body: { success: true, action, styleId } };
  } catch (error) {
    return { status: 500, body: { error: "Internal server error" } };
  }
}
