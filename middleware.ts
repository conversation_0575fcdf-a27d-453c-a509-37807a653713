import { NextResponse, NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // Redirect HTTP to HTTPS in production (except localhost)
  const host = request.headers.get('host');
  const isLocalhost = host && (host.startsWith('localhost') || host.startsWith('127.0.0.1'));
  if (!isLocalhost && request.nextUrl.protocol === 'http:') {
    const url = request.nextUrl.clone();
    url.protocol = 'https:';
    return NextResponse.redirect(url, 308);
  }

  // Dynamically get Supabase project ref and cookie name
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  let projectRef = '';
  if (supabaseUrl) {
    try {
      projectRef = supabaseUrl.replace('https://', '').split('.')[0];
    } catch (err) {
      projectRef = '';
    }
  }
  const cookieNameAuthToken = projectRef ? `sb-${projectRef}-auth-token` : '';

  // Auth protection logic for protected routes
  const protectedPaths = ['/studio', '/profile', '/community', '/gallery'];
  const { pathname } = request.nextUrl;
  const isProtected = protectedPaths.some((p) => pathname.startsWith(p));

  if (isProtected) {
    const hasSessionCookie = cookieNameAuthToken && request.cookies.has(cookieNameAuthToken);
    if (!hasSessionCookie) {
      const loginUrl = request.nextUrl.clone();
      loginUrl.pathname = '/login';
      loginUrl.search = '?reason=session-expired';
      return NextResponse.redirect(loginUrl);
    }
  }

  // Allow all other requests through
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}; 