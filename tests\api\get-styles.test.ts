// @ts-nocheck
import { getStylesHandler } from "../../lib/api/getStylesHandler";

jest.mock("@/lib/api/getStylesHandler", () => {
  return {
    getStylesHandler: jest.fn(() => ({
      data: { category1: [{ id: "style1", title: "Style 1", prompt: "Prompt 1" }] },
      error: undefined,
    })),
  };
});

describe("getStylesHandler", () => {
  it("returns styles from JSON", () => {
    jest.resetModules();
    const result = getStylesHandler();
    expect(result.data).toEqual({
      category1: [{ id: "style1", title: "Style 1", prompt: "Prompt 1" }],
    });
  });

  it("returns error if loading styles fails", () => {
    jest.resetModules();
    const result = getStylesHandler();
    expect(result.error).toBe(undefined);
  });
});
