import { Html, <PERSON>, Main, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en" className="dark">
      <Head>
        <meta name="color-scheme" content="dark" />
        <link rel="dns-prefetch" href="https://api.openai.com" />
        <link rel="dns-prefetch" href="https://supabase.co" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="manifest" href="/manifest.json" />
        {/* Twitter Card meta tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="PxlMorph AI - AI-Powered Image Generation" />
        <meta name="twitter:description" content="Create stunning AI-generated images with advanced style controls and real-time editing capabilities." />
        <meta name="twitter:image" content="https://pxlmorph.com/og-image.png" />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
