-- Drop existing SELECT policies for authenticated users on images
DROP POLICY IF EXISTS "Users can read own images" ON images;
DROP POLICY IF EXISTS "Users can read shared images" ON images;

-- Create a single combined SELECT policy for authenticated users
CREATE POLICY "Users can read own or shared images"
  ON images
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = user_id OR is_shared = true
  );