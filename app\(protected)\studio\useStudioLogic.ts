"use client";
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { SupabaseService, supabase } from "@/lib/supabase";
import { CacheService } from "@/lib/cache-service";
import { GeneratedImage, User } from "@/lib/types";
import { toast } from "sonner";
import {
  useRecentlyUsedStyles,
} from "@/lib/contexts/RecentlyUsedStylesContext";
import { useStyles } from "@/lib/contexts/StylesContext";
import { useRecentCreations } from "@/lib/hooks/useRecentCreations";
import { useUser } from "@/lib/contexts/UserContext";
import useSWR, { mutate } from "swr";
import { useImageSharing } from "@/lib/hooks/useImageSharing";
import { showGenerationToast } from "@/lib/utils/toast";
import { useImageGeneration } from "@/lib/hooks/useImageGeneration";
import { useAnalytics } from "@/lib/hooks/useAnalytics";
import { useGeneration } from "@/lib/contexts/GenerationContext";

// IndexedDB helper for persistent uploaded image cache
const UPLOAD_DB_NAME = "StudioUploadCacheDB";
const UPLOAD_STORE_NAME = "uploads";
let uploadDbPromise: Promise<IDBDatabase> | null = null;

function openUploadDb(): Promise<IDBDatabase> {
  if (uploadDbPromise) return uploadDbPromise;
  uploadDbPromise = new Promise((resolve, _reject) => {
    const request = indexedDB.open(UPLOAD_DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(UPLOAD_STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => _reject(request.error);
  });
  return uploadDbPromise;
}

async function setUploadCacheItem(key: string, value: any) {
  try {
    const db = await openUploadDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(UPLOAD_STORE_NAME, "readwrite");
      const store = tx.objectStore(UPLOAD_STORE_NAME);
      const req = store.put(value, key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch {
    // ignore
  }
}

async function getUploadCacheItem(key: string): Promise<any> {
  try {
    const db = await openUploadDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(UPLOAD_STORE_NAME, "readonly");
      const store = tx.objectStore(UPLOAD_STORE_NAME);
      const req = store.get(key);
      req.onsuccess = () => resolve(req.result);
      req.onerror = () => resolve(undefined);
    });
  } catch {
    return undefined;
  }
}

async function removeUploadCacheItem(key: string) {
  try {
    const db = await openUploadDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(UPLOAD_STORE_NAME, "readwrite");
      const store = tx.objectStore(UPLOAD_STORE_NAME);
      const req = store.delete(key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch {
    // ignore
  }
}

export function useStudioLogic() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingLocal, setIsGeneratingLocal] = useState(false); // Local state for button spinner
  // Removed local isGenerating, use context instead
  const [selectedStyle, setSelectedStyle] = useState("studio-ghibli");
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [customPrompt, setCustomPrompt] = useState("");
  const [generationProgress, setGenerationProgress] = useState("");
  const [clientIsOnline, setClientIsOnline] = useState(true);
  const [justGeneratedImageId, setJustGeneratedImageId] = useState<string | null>(null);
  const [hasUsedCurrentImage, setHasUsedCurrentImage] = useState(false);
  const [isClientSide, setIsClientSide] = useState(false);
  const [isApiConfigured, setIsApiConfigured] = useState<boolean>(true); // Default to true for SSR, will check on mount

  // Analytics hook
  const { trackStyleSelection, trackImageGeneration, trackEvent } =
    useAnalytics();

  // Recently used styles context
  const { addStyle: addRecentlyUsedStyle } = useRecentlyUsedStyles();

  // Styles context
  const { styles: allStyles } = useStyles();

  const { user: userContext } = useUser();
  const {
    recentImages,
    isLoading: loadingRecentImages,
    mutate: refreshRecentImages,
  } = useRecentCreations(userContext?.id);

  const { isGenerating, setIsGenerating, lastGeneratedImage } = useGeneration();

  // Memoize the recent images array to prevent unnecessary re-renders
  const recentImagesWithPlaceholder = useMemo(() => {
    if (
      isGenerating &&
      (!lastGeneratedImage || !recentImages.some(img => img.id === lastGeneratedImage.id))
    ) {
      return [{ id: 'generating', generating: true } as any, ...recentImages];
    }
    return recentImages;
  }, [isGenerating, lastGeneratedImage, recentImages]);

  // Save uploaded image to IndexedDB
  const saveImageToIndexedDb = useCallback(async (file: File) => {
    if (typeof window === "undefined") return;
    try {
      const reader = new FileReader();
      reader.onload = () => {
        const imageData = {
          name: file.name,
          type: file.type,
          size: file.size,
          data: reader.result as string,
          timestamp: Date.now(),
        };
        if (userContext?.id) {
          setUploadCacheItem(`pxlmorph_uploaded_image_${userContext.id}`, imageData);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Failed to save image to IndexedDB:", error);
    }
  }, [userContext?.id]);

  // Load image from IndexedDB
  const loadImageFromIndexedDb = useCallback(async () => {
    if (typeof window === "undefined") return;
    try {
      let imageData = null;
      if (userContext?.id) {
        imageData = await getUploadCacheItem(`pxlmorph_uploaded_image_${userContext.id}`);
      }
      if (imageData) {
        const response = await fetch(imageData.data);
        const blob = await response.blob();
        const file = new File([blob], imageData.name, { type: imageData.type });
        setUploadedImage(file);
      } else {
        // No image in cache, skip loading
        setUploadedImage(null);
      }
    } catch (error) {
      console.error("Failed to load image from IndexedDB:", error);
      if (userContext?.id) {
        removeUploadCacheItem(`pxlmorph_uploaded_image_${userContext.id}`);
      }
      setUploadedImage(null);
    }
  }, [userContext?.id]);

  // Clear upload cache on user change
  useEffect(() => {
    if (typeof window === "undefined") return;
    if (!userContext?.id) {
      // If no user, clear all possible upload caches (optional, or just skip)
      return;
    }
    // Optionally, clear previous user's cache here if needed
    // (not strictly necessary if key is user-specific)
  }, [userContext?.id]);

  // Persist selectedStyle in localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return;
    const savedStyle = localStorage.getItem('pxlmorph_selected_style');
    if (savedStyle) {
      setSelectedStyle(savedStyle);
    }
  }, []);

  useEffect(() => {
    // Mark as client-side after hydration
    setIsClientSide(true);
    // Check OpenAI API key config on mount
    fetch("/api/check-config")
      .then((res) => res.json())
      .then((data) => setIsApiConfigured(!!data.configured))
      .catch(() => setIsApiConfigured(false));
  }, []);

  useEffect(() => {
    if (!isClientSide) return;

    checkAuth();
    loadImageFromIndexedDb(); // Load saved image on mount

    setClientIsOnline(typeof window !== "undefined" ? navigator.onLine : true);
    const handleOnlineStatusChange = () =>
      setClientIsOnline(
        typeof window !== "undefined" ? navigator.onLine : true,
      );
    window.addEventListener("online", handleOnlineStatusChange);
    window.addEventListener("offline", handleOnlineStatusChange);

    // Listen for image share status changes from other pages
    const handleImageShareStatusChanged = (_event: CustomEvent) => {
      // Removed dev log
      // Trigger a refresh to get the latest data
      refreshRecentImages();
    };

    // Listen for image deletion events from other pages
    const handleImageDeleted = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (process.env.NODE_ENV === "development") {
        console.log(
          "[useStudioLogic] Received imageDeleted event:",
          customEvent.detail,
        );
      }
      // Trigger a refresh to get the latest data
      refreshRecentImages();
    };

    window.addEventListener(
      "imageShareStatusChanged",
      handleImageShareStatusChanged as EventListener,
    );
    window.addEventListener(
      "imageDeleted",
      handleImageDeleted as EventListener,
    );

    return () => {
      window.removeEventListener("online", handleOnlineStatusChange);
      window.removeEventListener("offline", handleOnlineStatusChange);
      window.removeEventListener(
        "imageShareStatusChanged",
        handleImageShareStatusChanged as EventListener,
      );
      window.removeEventListener(
        "imageDeleted",
        handleImageDeleted as EventListener,
      );
    };
  }, [loadImageFromIndexedDb, isClientSide, refreshRecentImages]);

  const checkAuth = async () => {
    try {
      const currentUser = await SupabaseService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error("Auth check failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshRecentImagesVoid = async () => { await refreshRecentImages(); };
  const imageGeneration = useImageGeneration({
    user,
    selectedStyle,
    uploadedImage,
    customPrompt,
    trackImageGeneration,
    trackEvent,
    addRecentlyUsedStyle,
    allStyles,
    refreshRecentImages: refreshRecentImagesVoid,
    loadImageFromIndexedDb,
  });

  // Use unified image sharing hook
  const { handleImageShare: baseHandleImageShare } = useImageSharing();

  // Optimistic update for sharing images
  const handleImageShare = useCallback(
    async (imageId: string, isShared: boolean) => {
      if (!user?.id) return;
      const success = await baseHandleImageShare(imageId, isShared);
      if (success) {
        // Trigger a refresh to ensure we have the latest data
        refreshRecentImages();
      }
    },
    [baseHandleImageShare, refreshRecentImages, user?.id],
  );

  // Optimistic update for deleting images
  const handleImageDelete = useCallback(
    async (imageId: string) => {
      // No-op: deletion is now handled in UnifiedImageCard. Optionally, use for analytics or UI notification.
      // Example: trackEvent('image_delete', { imageId });
    },
    []
  );

  const handleImageUpload = (file: File | null) => {
    setUploadedImage(file);
    if (file) {
      saveImageToIndexedDb(file); // Save to IndexedDB

      // Track image upload
      trackEvent("image_upload", {
        file_size: file.size,
        file_type: file.type,
        file_name: file.name,
      });
    } else {
      if (userContext?.id) {
        removeUploadCacheItem(`pxlmorph_uploaded_image_${userContext.id}`);
      }
    }
  };

  // Track style selection and persist to localStorage
  const handleStyleSelection = useCallback(
    (style: string) => {
      setSelectedStyle(style);
      if (typeof window !== 'undefined') {
        localStorage.setItem('pxlmorph_selected_style', style);
      }
      trackStyleSelection(style);
      if (typeof window !== 'undefined') {
        console.debug('[Studio] selectedStyle changed to:', style);
      }
    },
    [trackStyleSelection],
  );

  // Generate handler for the Transform button
  const handleGenerateImage = useCallback(async () => {
    setIsGeneratingLocal(true);
    setIsGenerating(true);
    try {
      await imageGeneration.generateImage();
    } finally {
      setIsGeneratingLocal(false);
      setIsGenerating(false);
    }
  }, [imageGeneration, setIsGenerating]);

  return {
    user,
    isLoading,
    isGenerating: isGeneratingLocal || isGenerating,
    isApiConfigured,
    clientIsOnline,
    generationProgress: imageGeneration.generationProgress,
    selectedStyle,
    uploadedImage,
    customPrompt,
    recentImages: recentImagesWithPlaceholder,
    hasLoadedRecentImages: !loadingRecentImages,
    setSelectedStyle: handleStyleSelection,
    setUploadedImage: handleImageUpload,
    setCustomPrompt,
    handleGenerateImage,
    handleImageShare,
    handleImageDelete,
    checkAuth,
    justGeneratedImageId,
    hasUsedCurrentImage,
    refreshRecentImages,
  };
}
