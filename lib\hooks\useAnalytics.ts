"use client";

import { useEffect, useCallback } from "react";
import { logEvent, setUserId, setUserProperties } from "firebase/analytics";
import { analytics } from "../firebase";

export const useAnalytics = () => {
  const trackEvent = useCallback(
    (eventName: string, parameters?: Record<string, any>) => {
      if (analytics) {
        logEvent(analytics, eventName, parameters);
      }
    },
    [],
  );

  const trackPageView = useCallback(
    (pageName: string, parameters?: Record<string, any>) => {
      if (analytics) {
        logEvent(analytics, "page_view", {
          page_name: pageName,
          ...parameters,
        });
      }
    },
    [],
  );

  const setUser = useCallback((userId: string) => {
    if (analytics) {
      setUserId(analytics, userId);
    }
  }, []);

  const setUserProps = useCallback((properties: Record<string, any>) => {
    if (analytics) {
      setUserProperties(analytics, properties);
    }
  }, []);

  // Track custom events for your app
  const trackImageGeneration = useCallback(
    (style: string) => {
      trackEvent("image_generation", {
        style,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  const trackImageDownload = useCallback(
    (imageId: string, style: string) => {
      trackEvent("image_download", {
        image_id: imageId,
        style,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  const trackStyleSelection = useCallback(
    (style: string) => {
      trackEvent("style_selection", {
        style,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  const trackUserLogin = useCallback(
    (method: string) => {
      trackEvent("user_login", {
        method,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  const trackUserSignup = useCallback(
    (method: string) => {
      trackEvent("user_signup", {
        method,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track user logout
  const trackUserLogout = useCallback(() => {
    trackEvent("user_logout", {
      timestamp: new Date().toISOString(),
    });
  }, [trackEvent]);

  // NEW: Track image view in modal
  const trackImageView = useCallback(
    (
      imageId: string,
      style: string,
      source: "gallery" | "community" | "studio",
    ) => {
      trackEvent("image_view", {
        image_id: imageId,
        style,
        source,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track search queries
  const trackSearch = useCallback(
    (query: string, page: string, resultsCount: number) => {
      trackEvent("search_query", {
        query,
        page,
        results_count: resultsCount,
        query_length: query.length,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track filter usage
  const trackFilterUsage = useCallback(
    (filterType: string, filterValue: string, page: string) => {
      trackEvent("filter_usage", {
        filter_type: filterType,
        filter_value: filterValue,
        page,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track app performance
  const trackPerformance = useCallback(
    (metric: string, value: number, unit: string) => {
      trackEvent("performance_metric", {
        metric,
        value,
        unit,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track error events
  const trackError = useCallback(
    (errorType: string, errorMessage: string, page: string) => {
      trackEvent("app_error", {
        error_type: errorType,
        error_message: errorMessage,
        page,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track user engagement
  const trackEngagement = useCallback(
    (action: string, duration?: number, page?: string) => {
      trackEvent("user_engagement", {
        action,
        duration,
        page,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  // NEW: Track feature usage
  const trackFeatureUsage = useCallback(
    (feature: string, action: string, page: string) => {
      trackEvent("feature_usage", {
        feature,
        action,
        page,
        timestamp: new Date().toISOString(),
      });
    },
    [trackEvent],
  );

  return {
    trackEvent,
    trackPageView,
    setUser,
    setUserProps,
    trackImageGeneration,
    trackImageDownload,
    trackStyleSelection,
    trackUserLogin,
    trackUserSignup,
    trackUserLogout,
    trackImageView,
    trackSearch,
    trackFilterUsage,
    trackPerformance,
    trackError,
    trackEngagement,
    trackFeatureUsage,
  };
};
