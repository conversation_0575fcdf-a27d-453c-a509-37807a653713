create policy "Users can read own data"
on "auth"."users"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = id));



create policy "Anyone can view images"
on "storage"."objects"
as permissive
for select
to authenticated
using ((bucket_id = 'private-images'::text));


create policy "User can view own and shared images 149xh4y_0"
on "storage"."objects"
as permissive
for select
to authenticated
using ((((storage.foldername(name))[1] = (auth.uid())::text) OR (EXISTS ( SELECT 1
   FROM images
  WHERE ((images.image_url ~~ (('%'::text || objects.name) || '%'::text)) AND (images.is_shared = true))))));


create policy "Users can delete own images"
on "storage"."objects"
as permissive
for delete
to authenticated
using (((bucket_id = 'private-images'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


create policy "Users can update own images"
on "storage"."objects"
as permissive
for update
to authenticated
using (((bucket_id = 'private-images'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


create policy "Users can upload images to own folder"
on "storage"."objects"
as permissive
for insert
to authenticated
with check (((bucket_id = 'private-images'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));



