import { useCallback } from "react";
import { SupabaseService } from "@/lib/supabase";
import { toast } from "sonner";
import { showShareToast } from "@/lib/utils/toast";

export function useImageSharing() {
  const handleImageShare = useCallback(
    async (imageId: string, isShared: boolean) => {
      // Show toast immediately for instant feedback
      showShareToast(isShared ? 'shared' : 'unshared');
      try {
        const success = await SupabaseService.toggleImageShare(
          imageId,
          isShared,
        );
        if (!success) {
          showShareToast('error');
          // Optionally: revert optimistic UI here if needed
          return false;
        }
        return true;
      } catch (error) {
        console.error("[useImageSharing] Error sharing image:", error);
        showShareToast('error');
        // Optionally: revert optimistic UI here if needed
        return false;
      }
    },
    [],
  );
  return { handleImageShare };
}
