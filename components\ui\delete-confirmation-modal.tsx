"use client";

import React from "react";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangle, Trash2, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isDeleting: boolean;
  itemName: string;
  description: string;
}

export function DeleteConfirmationModal({
  isOpen,
  onClose, // Make sure onClose resets ALL modal-related state, not just closes the modal
  onConfirm,
  isDeleting,
  itemName,
  description,
}: DeleteConfirmationModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md border-none bg-transparent p-0 shadow-none">
        <Card className="glass-card animate-fadeInUp border border-red-500/30 bg-slate-900/90 shadow-2xl backdrop-blur-2xl">
          <CardContent className="flex flex-col items-center p-8 pb-4">
            <div className="flex w-full flex-col items-center">
              <div className="relative mb-4">
                <div className="absolute inset-0 animate-pulse rounded-full bg-red-500/30 blur-2xl" />
                <div className="relative rounded-full border border-red-500/30 bg-red-500/20 p-5 shadow-lg">
                  <AlertTriangle className="animate-warning-pulse size-10 text-red-400 drop-shadow-lg" />
                </div>
              </div>
              <DialogTitle className="micro-bounce mb-2 text-center text-2xl font-bold text-red-500">
                Delete <span className="text-red-500">{itemName}</span>?
              </DialogTitle>
              <DialogDescription className="mb-2 text-center text-base leading-relaxed text-slate-300 fade-in">
                {description ??
                  <>Are you sure? This will&nbsp;
                    <span className="font-semibold text-red-400">delete&nbsp;{itemName}</span>
                    &nbsp;for good—no take-backs!</>
                }
              </DialogDescription>            </div>
            <div className="mt-6 flex w-full items-center gap-4">
              <Button
                variant="ghost"
                onClick={onClose}
                disabled={isDeleting}
                className="flex-1 border border-transparent text-slate-400 transition-all duration-150 hover:bg-slate-800/60 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={onConfirm}
                disabled={isDeleting}
                className={cn(
                  `flex-1 glass font-bold text-base flex items-center justify-center gap-2 py-2 px-4 rounded-xl border-0 text-white shadow-xl transition-all duration-300 btn-glow-red hover-lift focus:outline-none focus-visible:ring-4 focus-visible:ring-red-400/60 bg-slate-900/80
                  hover:bg-gradient-to-r hover:from-red-600 hover:to-red-400
                  focus:bg-gradient-to-r focus:from-red-600 focus:to-red-400
                  min-w-[200px]`,
                  isDeleting && "opacity-60 cursor-not-allowed"
                )}
                autoFocus
              >
                {isDeleting ? (
                  <>
                    <RefreshCw className="mr-2 size-5 animate-spin" />
                    Zapping...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 size-5 text-white drop-shadow" />
                    Yes, delete it!
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}

// Add the gradientShift keyframes if not already present
if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `@keyframes gradientShift { 0%,100%{background-position:0% 50%} 50%{background-position:100% 50%} }`;
  document.head.appendChild(style);
}
