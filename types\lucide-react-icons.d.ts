declare module 'lucide-react/dist/esm/icons/download' {
  import { FC, SVGProps } from 'react';
  const Download: FC<SVGProps<SVGSVGElement>>;
  export default Download;
}
declare module 'lucide-react/dist/esm/icons/share-2' {
  import { FC, SVGProps } from 'react';
  const Share2: FC<SVGProps<SVGSVGElement>>;
  export default Share2;
}
declare module 'lucide-react/dist/esm/icons/trash-2' {
  import { FC, SVGProps } from 'react';
  const Trash2: FC<SVGProps<SVGSVGElement>>;
  export default Trash2;
}
declare module 'lucide-react/dist/esm/icons/calendar' {
  import { FC, SVGProps } from 'react';
  const Calendar: FC<SVGProps<SVGSVGElement>>;
  export default Calendar;
}
declare module 'lucide-react/dist/esm/icons/sparkles' {
  import { FC, SVGProps } from 'react';
  const Sparkles: FC<SVGProps<SVGSVGElement>>;
  export default Sparkles;
}
declare module 'lucide-react/dist/esm/icons/heart' {
  import { FC, SVGProps } from 'react';
  const Heart: FC<SVGProps<SVGSVGElement>>;
  export default Heart;
}
declare module 'lucide-react/dist/esm/icons/refresh-cw' {
  import { FC, SVGProps } from 'react';
  const RefreshCw: FC<SVGProps<SVGSVGElement>>;
  export default RefreshCw;
}
declare module 'lucide-react/dist/esm/icons/log-in' {
  import { FC, SVGProps } from 'react';
  const LogIn: FC<SVGProps<SVGSVGElement>>;
  export default LogIn;
}
