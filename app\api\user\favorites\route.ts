import { NextRequest, NextResponse } from "next/server";
import {
  getUserFavorites<PERSON><PERSON><PERSON>,
  postUserFavoritesHandler,
} from "@/lib/api/userFavoritesHandler";
import { headers } from "next/headers";

export async function GET(_request: NextRequest) {
  const requestHeaders = await headers();
  const { status, body } = await getUserFavoritesHandler({
    headers: requestHeaders,
  });
  return NextResponse.json(body, { status });
}

export async function POST(request: NextRequest) {
  const requestHeaders = await headers();
  const { styleId, action } = await request.json();
  const { status, body } = await postUserFavoritesHandler({
    headers: requestHeaders,
    styleId,
    action,
  });
  return NextResponse.json(body, { status });
}
