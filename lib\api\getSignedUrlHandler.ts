import { createClient } from "@supabase/supabase-js";

export async function getSignedUrlHandler({
  headers,
  body,
  env,
}: {
  headers: any;
  body: any;
  env: any;
}) {
  const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    return {
      status: 500,
      body: {
        error: "Server configuration error: Missing Supabase credentials",
      },
    };
  }

  const authHeader = headers["authorization"] || headers["Authorization"];
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { status: 401, body: { error: "Authentication required" } };
  }
  const token = authHeader.replace("Bearer ", "");

  // Inline initialization per request
  const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
    auth: { autoRefreshToken: false, persistSession: false },
  });

  const {
    data: { user },
    error: authError,
  } = await supabaseServer.auth.getUser(token);
  if (authError || !user) {
    return { status: 401, body: { error: "Invalid authentication token" } };
  }

  const { filePath, expiresIn = 3600 } = body;
  if (!filePath) {
    return { status: 400, body: { error: "File path is required" } };
  }
  const pathParts = filePath.split("/");
  if (pathParts.length < 2) {
    return { status: 400, body: { error: "Invalid file path format" } };
  }
  const ownerId = pathParts[0];
  const fileName = pathParts.slice(1).join("/");

  const { data: imageData, error: dbError } = await supabaseServer
    .from("images")
    .select("id, user_id, image_url, is_shared, created_at")
    .eq("user_id", ownerId)
    .eq("image_url", `private-images/${ownerId}/${fileName}`)
    .maybeSingle();
  if (dbError) {
    return {
      status: 500,
      body: { error: "Database error", details: dbError.message },
    };
  }
  if (!imageData) {
    return { status: 404, body: { error: "Image not found in database" } };
  }
  const isOwner = imageData.user_id === user.id;
  const isShared = !!imageData.is_shared;
  if (!isOwner && !isShared) {
    return { status: 403, body: { error: "Access denied" } };
  }
  const { data: signedUrlData, error: signedUrlError } =
    await supabaseServer.storage
      .from("private-images")
      .createSignedUrl(`${ownerId}/${fileName}`, expiresIn);

  if (signedUrlError) {
    console.error("Error generating signed URL:", signedUrlError);
    return {
      status: 500,
      body: { error: "Failed to generate signed URL", details: signedUrlError.message },
    };
  }

  return {
    status: 200,
    body: { signedUrl: signedUrlData.signedUrl },
  };
}
