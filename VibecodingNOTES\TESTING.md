# Testing Documentation

## Overview

This project uses a comprehensive testing strategy with <PERSON><PERSON>, React Testing Library, and <PERSON><PERSON> to ensure code quality and reliability.

## Testing Tools

### Core Testing Framework

- **Jest**: JavaScript testing framework for unit and integration tests
- **@testing-library/react**: React component testing utilities
- **@testing-library/jest-dom**: Custom Jest matchers for DOM testing
- **@testing-library/user-event**: Advanced user interaction simulation

### Additional Tools

- **jsdom**: DOM environment for Node.js testing
- **SWR**: Data fetching library with built-in testing support
- **Playwright**: End-to-end testing (separate from Jest tests)

## Test Structure

### File Organization

```
tests/
├── unified-image-card.test.tsx      # Component tests
├── unified-image-modal.test.tsx     # Component tests
├── optimized-image.test.tsx         # Component tests
├── useImageLikeStatus.test.ts       # Hook tests
├── e2e/                            # Playwright E2E tests
│   ├── community.spec.ts            # E2E: Community page
│   ├── example.spec.ts              # E2E: Landing page
│   ├── gallery.spec.ts              # E2E: Gallery page
│   ├── login.spec.ts                # E2E: Login page
│   ├── not-found.spec.ts            # E2E: 404 page
│   └── studio.spec.ts               # E2E: Studio page
└── api/                            # (empty, reserved for future API tests)
```

### Naming Conventions

- **Jest Tests**: `*.test.tsx` or `*.test.ts`
- **Playwright Tests**: `*.spec.ts`
- **Component Tests**: `{ComponentName}.test.tsx`
- **Hook Tests**: `use{HookName}.test.ts`

### Test Structure Pattern

```typescript
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";

describe("ComponentName", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render correctly", () => {
    // Test implementation
  });

  it("should handle user interactions", () => {
    // Test implementation
  });
});
```

## Running Tests

### All Tests

```bash
npm test
# or
yarn test
```

### Specific Test Files

```bash
# Run specific component tests
npm test tests/unified-image-modal.test.tsx

# Run all component and hook tests
npm test tests/unified-image-card.test.tsx tests/unified-image-modal.test.tsx tests/optimized-image.test.tsx tests/useImageLikeStatus.test.ts

# Run with coverage
npm test -- --coverage
```

### Watch Mode

```bash
npm test -- --watch
# or
npm test -- --watchAll
```

### Playwright E2E Tests

```bash
npx playwright test
# All E2E tests are located in tests/e2e/
```

## Test Coverage Status

### Current Coverage (Jest Tests)

- **12 API handler test suites present**
- **All tests passing**
- **1 test skipped** (see below)

### API Handler Test Coverage

| Handler/Test File      | Status      | Coverage Notes                         |
| ---------------------- | ----------- | -------------------------------------- |
| check-config.test.ts   | ✅ All pass | Env/config checks, missing/valid input |
| debug-gallery.test.ts  | ✅ All pass | Auth, DB/storage, RLS, sync, error     |
| delete-image.test.ts   | ✅ All pass | Auth, input, UUID, success             |
| edit-image.test.ts     | ✅ All pass | Auth, input, OpenAI, success           |
| generate-image.test.ts | ✅ All pass | Auth, input, OpenAI, storage, success  |
| get-signed-url.test.ts | ✅ All pass | Auth, input, storage, error            |
| get-styles.test.ts     | ✅ All pass | JSON load, error                       |
| image-proxy.test.ts    | ✅ All pass | Auth, access, storage, binary, error   |
| images-like.test.ts    | ✅ All pass | Auth, input, like/unlike, error        |
| images-likes.test.ts   | ✅ All pass | Input, like count, error               |
| share-image.test.ts    | ✅ All pass | Input, DB error, success               |
| user-favorites.test.ts | ✅ All pass | Auth, input, add/remove, error         |

**All API handler tests in `tests/api/` are robust, cover all major cases (success, 400, 401, custom errors), and pass. See the 'API Handler Test Patterns (Best Practice)' section for how to add or update handler tests.**

### Component & Hook Coverage

| File               | Status                 | Notes                      |
| ------------------ | ---------------------- | -------------------------- |
| UnifiedImageModal  | ✅ All pass, 1 skipped | 93% (1 skipped: see below) |
| UnifiedImageCard   | ✅ All pass            |                            |
| OptimizedImage     | ✅ All pass            |                            |
| useImageLikeStatus | ✅ All pass            |                            |

### Playwright E2E Coverage

| File              | Description    |
| ----------------- | -------------- |
| login.spec.ts     | Login page     |
| example.spec.ts   | Landing page   |
| gallery.spec.ts   | Gallery page   |
| community.spec.ts | Community page |
| not-found.spec.ts | 404 page       |
| studio.spec.ts    | Studio page    |

### Skipped Tests

#### 1. "calls onClose when close button is clicked" (UnifiedImageModal)

**Reason**: The Dialog component doesn't render a visible close button by default. The modal can be closed by clicking outside or pressing Escape, but there's no dedicated close button in the UI.

**Status**: Intentionally skipped with TODO comment explaining the component design decision.

## Mocking Strategy

### External Dependencies

```typescript
// Next.js Image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props) => React.createElement("img", props),
}));

// Supabase client
jest.mock("@/lib/supabase", () => ({
  supabase: {
    auth: {
      getSession: jest.fn(() =>
        Promise.resolve({ data: { session: { user: mockUser } } }),
      ),
    },
  },
}));

// SWR
jest.mock("swr", () => ({
  __esModule: true,
  default: (key, fetcher) => ({
    data: mockData,
    error: null,
    mutate: jest.fn(),
  }),
}));
```

### Context Providers

```typescript
function Providers({ children }: { children: ReactNode }) {
  return (
    <UserProvider>
      <StylesProvider>
        {children}
      </StylesProvider>
    </UserProvider>
  );
}
```

### Global Mocks

```typescript
// IntersectionObserver for jsdom
global.IntersectionObserver = class {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};
```

## Test Categories

### 1. Component Tests

- **Render Tests**: Verify components render without crashing
- **Props Tests**: Test different prop combinations
- **User Interaction Tests**: Test click handlers, form submissions
- **State Tests**: Test component state changes
- **Accessibility Tests**: Verify ARIA labels and keyboard navigation

### 2. Hook Tests

- **Return Value Tests**: Verify hook returns expected values
- **Side Effect Tests**: Test useEffect and other side effects
- **State Update Tests**: Test state changes and re-renders
- **Error Handling Tests**: Test error scenarios

### 3. Integration Tests

- **Component Interaction**: Test how components work together
- **Context Integration**: Test components with providers
- **API Integration**: Test data fetching and caching

## Best Practices

### Test Organization

1. **Arrange**: Set up test data and mocks
2. **Act**: Perform the action being tested
3. **Assert**: Verify the expected outcome

### Naming Conventions

- Use descriptive test names that explain the behavior
- Group related tests with `describe` blocks
- Use `it` for individual test cases

### Mocking Guidelines

- Mock external dependencies, not internal logic
- Use realistic mock data
- Reset mocks between tests with `beforeEach`

### Accessibility Testing

- Test keyboard navigation
- Verify ARIA labels and roles
- Test screen reader compatibility

## Future Testing Recommendations

### 1. Integration Tests

```typescript
// Test component interactions
describe("Gallery Integration", () => {
  it("should open modal when card is clicked", () => {
    // Test UnifiedImageCard -> UnifiedImageModal flow
  });
});
```

### 2. Visual Regression Tests

```bash
# Set up visual testing with Chromatic or Percy
npm install --save-dev @chromatic-com/storybook
```

### 3. Performance Tests

```typescript
// Test component rendering performance
import { render } from '@testing-library/react';
import { performance } from 'perf_hooks';

it('should render within performance budget', () => {
  const start = performance.now();
  render(<Component />);
  const end = performance.now();
  expect(end - start).toBeLessThan(100); // 100ms budget
});
```

### 4. E2E Test Expansion

- Add more Playwright tests for critical user flows
- Test authentication flows
- Test image generation and sharing workflows

### 5. API Testing

- Add API endpoint tests in `tests/api/` (currently empty)

### 6. Error Boundary Testing

```typescript
// Test error boundaries
it("should render fallback UI when component crashes", () => {
  // Test error boundary behavior
});
```

## API Handler Test Patterns (Best Practice)

To ensure robust, maintainable, and future-proof API handler tests, follow these patterns:

### 1. File Structure & Naming

- Place each handler’s test in `tests/api/{handler-name}.test.ts`.
- Name the test after the handler for clarity and discoverability.

### 2. Mocking External Dependencies

- **Supabase:**
  - Define all mock functions (`mockGetUser`, `mockFrom`, etc.) **above** the `jest.mock` call.
  - Use a factory function for the client:
    ```js
    const mockCreateClient = jest.fn(() => ({
      auth: { getUser: mockGetUser },
      from: mockFrom,
      storage: mockStorage,
    }));
    ```
  - Place `jest.mock('@supabase/supabase-js', ...)` at the very top, referencing the already-defined mocks.
- **Other services:**
  - Mock OpenAI, fetch, etc., using the same pattern.

### 3. Globals & Environment

- At the top of each test file:
  ```js
  if (typeof global.Buffer === "undefined")
    global.Buffer = require("buffer").Buffer;
  if (typeof global.Headers === "undefined")
    global.Headers = require("node-fetch").Headers;
  process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
  process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";
  ```

### 4. Chained Mocks

- For handlers that chain `.from().select().eq().eq().maybeSingle()` or similar, ensure each method returns an object with the next method in the chain, and the final one returns a Promise with the expected data.

### 5. Import Order for ESM Mocks

- If a handler still doesn’t pick up the mock:
  - Remove the top-level import.
  - Use `jest.resetModules()` in `beforeEach`.
  - Use `const { handler } = require('...')` inside each test.

### 6. Test Coverage

- Cover:
  - Success (200)
  - Bad input (400)
  - Unauthorized (401)
  - Custom errors (e.g., 404, 500, DB errors)
- Use descriptive test names and group related tests with `describe`.

### 7. Debugging

- Add `console.log` for handler results if a test fails unexpectedly.
- Check the handler’s destructuring and return shape to match your mocks.

#### Template Example

```js
// Mocks
const mockGetUser = jest.fn(...);
const mockFrom = jest.fn(...);
const mockCreateClient = jest.fn(() => ({ ... }));

jest.mock('@supabase/supabase-js', () => ({
  __esModule: true,
  createClient: mockCreateClient,
}));

if (typeof global.Buffer === 'undefined') global.Buffer = require('buffer').Buffer;
if (typeof global.Headers === 'undefined') global.Headers = require('node-fetch').Headers;
process.env.NEXT_PUBLIC_SUPABASE_URL = '...';
process.env.SUPABASE_SERVICE_ROLE_KEY = '...';

describe('handlerName', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  it('should ...', async () => {
    const { handlerName } = require('@/lib/api/handlerName');
    // test logic
  });
});
```

**Following these patterns will ensure:**

- All handlers are testable, maintainable, and robustly covered.
- You avoid common Jest/ESM mocking pitfalls.
- Future contributors can easily add or update tests.

## Continuous Integration

### GitHub Actions Setup

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm ci
      - run: npm test
      - run: npm run test:e2e
```

### Pre-commit Hooks

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm test -- --watchAll=false"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Mock not working**: Ensure mocks are defined before imports
2. **Async test failures**: Use `waitFor` for async operations
3. **Context errors**: Wrap components in necessary providers
4. **Image loading issues**: Mock `next/image` component

### Debug Commands

```bash
# Run tests with verbose output
npm test -- --verbose

# Run specific test with debugging
npm test -- --testNamePattern="should render correctly"

# Run tests with coverage report
npm test -- --coverage --watchAll=false
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing](https://testing-library.com/docs/guide-accessibility)
