import { useCallback, useState } from "react";
import { showDownloadToast } from "@/lib/utils/toast";
import { formatDownloadFileName } from "@/lib/utils/format";
import { supabase } from "@/lib/supabase";
import { errorHandler } from "@/lib/utils/errorHandler";

/**
 * useImageDownload - Hook for downloading images with auth, formatting, and toast feedback.
 * @returns {Object} { downloadImage, isDownloading }
 */
export function useImageDownload() {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadImage = useCallback(
    async ({
      displayUrl,
      styleName,
      createdAt,
      imageId,
      trackImageDownload,
    }: {
      displayUrl: string;
      styleName: string;
      createdAt: string | Date;
      imageId: string;
      trackImageDownload?: (id: string, style: string) => void;
    }) => {
      if (isDownloading || imageId === 'generating' || !displayUrl) {
        if (imageId === 'generating') console.warn('Download attempted for placeholder image');
        return;
      }
      setIsDownloading(true);
      showDownloadToast(true, 'starting'); // Show toast immediately

      try {
        const shortId = imageId ? imageId.slice(-6) : Math.random().toString(36).slice(-6);
        const fileName = formatDownloadFileName(styleName, createdAt ? createdAt : "-", shortId);
        
        // Directly use the displayUrl which is already a blob URL from the cache
        const a = document.createElement("a");
        a.href = displayUrl;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // We don't need to revoke the blob URL here as it's managed by the image cache service
        // and is reused across components.

        trackImageDownload?.(imageId, styleName);
        showDownloadToast(true, 'success');
      } catch (error) {
        errorHandler(error, { userMessage: "Failed to download image." });
        showDownloadToast(false);
      } finally {
        setIsDownloading(false);
      }
    },
    [isDownloading]
  );
  
  return { downloadImage, isDownloading };
}
