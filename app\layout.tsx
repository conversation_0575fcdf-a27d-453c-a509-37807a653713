import "./globals.css";
import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import React, { Suspense } from 'react';
import { Toaster } from "@/components/ui/sonner";
import { StylesProvider } from "@/lib/contexts/StylesContext";
import { UserProvider } from "@/lib/contexts/UserContext";
import { ServiceWorkerRegistration } from "@/components/ServiceWorkerRegistration";
import { PageViewTracker } from "@/components/analytics/PageViewTracker";
import { SWRConfig } from "swr";
import AppBackground from "@/components/layout/BackgroundWrapper";
import ScrollToTop from "@/components/ScrollToTop";
import { GenerationProvider } from "@/lib/contexts/GenerationContext";
import { GlobalGeneratedImageModal } from "@/components/GlobalGeneratedImageModal";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "PxlMorph AI - AI-Powered Image Generation",
  description:
    "Create stunning AI-generated images with advanced style controls and real-time editing capabilities.",
  keywords: ["AI", "image generation", "DALL-E", "art", "creative", "design"],
  authors: [{ name: "PxlMorph AI" }],
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000",
  ),
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title: "PxlMorph AI - AI-Powered Image Generation",
    description:
      "Create stunning AI-generated images with advanced style controls and real-time editing capabilities.",
    siteName: "PxlMorph AI",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#8b5cf6",
  colorScheme: "dark",
};

export const dynamic = "force-dynamic";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
    const originalWarn = console.warn;
    console.warn = function (...args) {
      if (
        typeof args[0] === "string" &&
        (args[0].includes("Deprecation warning: use moment.updateLocale") ||
          args[0].includes("moment.defineLocale"))
      ) {
        return;
      }
      originalWarn.apply(console, args);
    };
  }

  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} h-screen w-full overflow-x-hidden font-sans antialiased`}>
        <Suspense fallback={null}>
          <Toaster theme="dark" position="top-right" toastOptions={{
            style: {
              background: "rgba(15, 23, 42, 0.9)",
              border: "1px solid rgba(139, 92, 246, 0.2)",
              color: "#f1f5f9",
            },
          }} />
        </Suspense>
        <ScrollToTop />
        <AppBackground />
        <SWRConfig
          value={{
            revalidateOnFocus: false,
            revalidateOnReconnect: true,
            dedupingInterval: 5 * 60 * 1000,
            errorRetryCount: 3,
            errorRetryInterval: 1000,
          }}
        >
          <StylesProvider>
            <UserProvider>
              <GenerationProvider>
                <PageViewTracker>
                  {children}
                </PageViewTracker>
                <GlobalGeneratedImageModal />
              </GenerationProvider>
            </UserProvider>
          </StylesProvider>
        </SWRConfig>
        <SpeedInsights />
        <Analytics />
        <ServiceWorkerRegistration />
      </body>
    </html>
  );
}
