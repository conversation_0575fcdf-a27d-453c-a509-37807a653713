import { toast } from "sonner";

/**
 * Shows a toast for like or dislike actions.
 * @param liked - true if liked, false if unliked
 */
export function showLikeToast(liked: boolean) {
  if (liked) {
    toast.success("❤️ Image liked!");
  } else {
    toast.info("💔 Like removed.");
  }
}

/**
 * Shows a toast for image deletion.
 * @param success - true if deleted, false if failed
 */
export function showDeleteToast(success: boolean) {
  if (success) {
    toast.success("🗑️ Image deleted!");
  } else {
    toast.error("Failed to delete image");
  }
}

/**
 * Shows a toast for image download.
 * @param success - true if downloaded, false if failed
 * @param stage - 'starting' or 'success'
 */
export function showDownloadToast(success: boolean, stage: 'starting' | 'success' = 'success') {
  if (stage === 'starting') {
    toast.info("Starting download...");
  } else if (success) {
    toast.success("✅ Download complete!");
  } else {
    toast.error("Download failed.");
  }
}

/**
 * Shows a toast for sharing or copying a link.
 * @param type - 'shared', 'unshared', 'copied', or 'error'
 */
export function showShareToast(type: 'shared' | 'unshared' | 'copied' | 'error') {
  if (type === 'shared') {
    toast.success("🚀 Shared to community!");
  } else if (type === 'unshared') {
    toast.info("🙈 Unshared from community.");
  } else if (type === 'copied') {
    toast.success("✅ Link copied!");
  } else {
    toast.error("Failed to copy link.");
  }
}

/**
 * Shows a toast for authentication actions.
 * @param type - 'signout-success' | 'signout-error' | 'login-required'
 */
export function showAuthToast(type: 'signout-success' | 'signout-error' | 'login-required') {
  if (type === 'signout-success') {
    toast.success('Signed out successfully');
  } else if (type === 'signout-error') {
    toast.error('Failed to sign out');
  } else if (type === 'login-required') {
    toast.error('You need to be logged in to view your profile.');
  }
}

/**
 * Shows a toast for image generation actions.
 * @param type - 'start' | 'success' | 'saved' | 'error' | 'blocked'
 */
export function showGenerationToast(
  type: 'start' | 'success' | 'saved' | 'error' | 'blocked'
) {
  if (type === 'start') {
    toast.info('🎨 Generating your masterpiece...');
  } else if (type === 'success') {
    toast.success('✨ Masterpiece created!');
  } else if (type === 'saved') {
    toast.success('✅ Artwork saved to your gallery!');
  } else if (type === 'blocked') {
    toast.error('Blocked by AI Safety System');
  } else if (type === 'error') {
    toast.error('Failed to generate image');
  }
}


/**
 * Shows a warning toast for cache/local storage issues.
 */
export function showCacheWarningToast() {
  toast.warning("Could not save data locally.");
}
