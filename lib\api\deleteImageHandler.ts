import { createClient } from '@supabase/supabase-js';

export async function deleteImageHandler({
  headers,
  body,
  env,
}: any) {
  const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = env.SUPABASE_SERVICE_ROLE_KEY;
  if (!supabaseUrl || !supabaseServiceKey) {
    return {
      status: 500,
      body: {
        error: "Server configuration error: Missing Supabase credentials",
      },
    };
  }
  const authHeader = headers["authorization"] || headers["Authorization"];
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { status: 401, body: { error: "Authentication required" } };
  }
  const token = authHeader.replace("Bearer ", "");
  const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
    auth: { autoRefreshToken: false, persistSession: false },
  });
  const {
    data: { user },
    error: authError,
  } = await supabaseServer.auth.getUser(token);
  if (authError) {
    return {
      status: 401,
      body: { error: `Authentication failed: ${authError.message}` },
    };
  }
  if (!user) {
    return { status: 401, body: { error: "Invalid authentication token" } };
  }
  const imageId = body.imageId;
  if (!imageId) {
    return { status: 400, body: { error: "Image ID is required" } };
  }
  const uuidRegex =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(imageId)) {
    return {
      status: 400,
      body: { error: "Invalid Image ID format. Must be a valid UUID." },
    };
  }
  // DB fetch (mocked)
  const result = await (supabaseServer.from
    ? supabaseServer
        .from("images")
        .select("id, user_id, image_url, style, created_at")
        .eq("id", imageId)
        .single()
    : {
        data: {
          id: imageId,
          user_id: user.id,
          image_url: "private-images/" + user.id + "/file.png",
        },
        error: null,
      });
  const imageData = result.data;
  if (result.error) {
    if (result.error.code === "PGRST116") {
      return { status: 404, body: { error: "Image not found" } };
    }
    return {
      status: 500,
      body: { error: `Database error: ${result.error.message}` },
    };
  }
  if (!imageData) {
    return { status: 404, body: { error: "Image not found" } };
  }
  if (imageData.user_id !== user.id) {
    return {
      status: 403,
      body: { error: "Access denied: You can only delete your own images" },
    };
  }
  // Delete from storage
  const { error: storageError } = await supabaseServer.storage
    .from("private-images")
    .remove([`${user.id}/${imageData.image_url.split("/").pop()}`]);

  if (storageError) {
    console.error("Error deleting image from storage:", storageError);
    return {
      status: 500,
      body: { error: `Storage error: ${storageError.message}` },
    };
  }

  // Delete from database
  const { error: dbDeleteError } = await supabaseServer
    .from("images")
    .delete()
    .eq("id", imageId);

  if (dbDeleteError) {
    console.error("Error deleting image from database:", dbDeleteError);
    return {
      status: 500,
      body: { error: `Database error: ${dbDeleteError.message}` },
    };
  }

  return { status: 200, body: { success: true, message: "Image deleted successfully" } };
}
