// /app/api/get-styles/route.ts
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Define the expected structure of the JSON data
interface Style {
  id: string;
  title: string;
  prompt: string;
  example_image?: string;
}

interface StyleData {
  [category: string]: Style[];
}

export async function GET() {
  try {
    const filePath = path.join(process.cwd(), 'style_prompts.json');
    const file = await fs.promises.readFile(filePath, 'utf-8');
    const data: StyleData = JSON.parse(file);
    
    const response = NextResponse.json(data);
    // Add caching headers for performance
    response.headers.set(
      "Cache-Control",
      "public, max-age=3600, s-maxage=3600, stale-while-revalidate=86400",
    );
    response.headers.set(
      "ETag",
      `"styles-${Object.keys(data).length}-${new Date().getTime()}"`,
    );
    return response;

  } catch (error) {
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ error: 'An unknown error occurred' }, { status: 500 });
  }
}
