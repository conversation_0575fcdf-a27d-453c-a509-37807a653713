// Centralized API service for all HTTP requests
// Usage: const { data, error } = await apiService.get('/api/endpoint');

export type ApiResponse<T> = {
  data: T | null;
  error: string | null;
  status: number;
};

export const apiService = {
  async request<T>(
    url: string,
    options: RequestInit = {},
    parseJson: boolean = true
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, options);
      let data: T | null = null;
      if (parseJson) {
        data = await response.json();
      }
      if (!response.ok) {
        return {
          data: null,
          error: (data as any)?.error || response.statusText,
          status: response.status,
        };
      }
      return { data, error: null, status: response.status };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Network error',
        status: 0,
      };
    }
  },

  get<T>(url: string, headers: HeadersInit = {}) {
    return this.request<T>(url, { method: 'GET', headers });
  },

  post<T>(url: string, body: any, headers: HeadersInit = {}) {
    return this.request<T>(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', ...headers },
      body: JSON.stringify(body),
    });
  },

  put<T>(url: string, body: any, headers: HeadersInit = {}) {
    return this.request<T>(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json', ...headers },
      body: JSON.stringify(body),
    });
  },

  delete<T>(url: string, headers: HeadersInit = {}) {
    return this.request<T>(url, { method: 'DELETE', headers });
  },
}; 