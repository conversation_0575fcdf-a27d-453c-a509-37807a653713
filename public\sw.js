const CACHE_NAME = "pxlmorph-ai-v1";
const STATIC_CACHE_NAME = "pxlmorph-ai-static-v1";
const DYNAMIC_CACHE_NAME = "pxlmorph-ai-dynamic-v1";

// Assets to cache immediately
const STATIC_ASSETS = [
  "/",
  "/offline.html",
  "/placeholder-style.png",
];

// File extensions to cache
const CACHEABLE_EXTENSIONS = [
  ".js",
  ".css",
  ".png",
  ".jpg",
  ".jpeg",
  ".gif",
  ".svg",
  ".woff",
  ".woff2",
  ".ttf",
  ".eot",
];

// API endpoints that should NOT be cached
const NO_CACHE_ENDPOINTS = [
  "/api/generate-image",
  "/api/edit-image",
  "/api/delete-image",
  "/api/user/favorites",
  "/api/test-",
];

// Install event - cache static assets
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME).then((cache) => {
      return Promise.allSettled(
        STATIC_ASSETS.map((url) =>
          cache.add(url).catch((err) => {
            console.error("Failed to cache:", url, err);
          }),
        ),
      );
    }),
  );
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (
            cacheName !== STATIC_CACHE_NAME &&
            cacheName !== DYNAMIC_CACHE_NAME
          ) {
            return caches.delete(cacheName);
          }
        }),
      );
    }),
  );
  self.clients.claim();
});

// Fetch event - handle requests
self.addEventListener("fetch", (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== "GET") {
    return;
  }

  // Skip API calls that should not be cached
  if (isNoCacheEndpoint(url.pathname)) {
    return;
  }

  // Handle different types of requests
  if (isStaticAsset(url.pathname)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isImageRequest(url.pathname)) {
    event.respondWith(handleImageRequest(request));
  } else {
    event.respondWith(handlePageRequest(request));
  }
});

// Check if endpoint should not be cached
function isNoCacheEndpoint(pathname) {
  return NO_CACHE_ENDPOINTS.some((endpoint) => pathname.startsWith(endpoint));
}

// Check if request is for a static asset
function isStaticAsset(pathname) {
  return (
    CACHEABLE_EXTENSIONS.some((ext) => pathname.endsWith(ext)) ||
    pathname.startsWith("/_next/")
  );
}

// Check if request is for an image
function isImageRequest(pathname) {
  return (
    /\.(png|jpg|jpeg|gif|svg|webp|avif)$/i.test(pathname) ||
    pathname.startsWith("/api/image-proxy/")
  );
}

// Handle static asset requests
async function handleStaticAsset(request) {
  try {
    // Try network first, fallback to cache
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      if (request.url.startsWith('http')) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }
  } catch (error) {
    console.log("Network failed for static asset, trying cache:", request.url);
  }

  // Fallback to cache
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Return offline page for navigation requests
  if (request.mode === "navigate") {
    return caches.match("/offline.html");
  }

  return new Response("Not found", { status: 404 });
}

// Handle image requests
async function handleImageRequest(request) {
  try {
    // Try cache first for images
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // If not in cache, try network
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      if (request.url.startsWith('http')) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }
  } catch (error) {
    console.log("Failed to fetch image:", request.url);
  }

  // Return placeholder image
  return caches.match("/placeholder-style.png");
}

// Handle page requests
async function handlePageRequest(request) {
  try {
    // Try network first for pages
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      if (request.url.startsWith('http')) {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }
  } catch (error) {
    console.log("Network failed for page, trying cache:", request.url);
  }

  // Fallback to cache
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Return offline page
  return caches.match("/offline.html");
}

// Background sync for failed requests
self.addEventListener("sync", (_event) => {
  if (_event.tag === "background-sync") {
    _event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Implement background sync logic here
  console.log("Background sync triggered");
}

// Push notification handling
self.addEventListener("push", (_event) => {
  const options = {
    body: _event.data ? _event.data.text() : "New notification from StyleGen AI",
    icon: "/placeholder-style.png",
    badge: "/placeholder-style.png",
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: "explore",
        title: "View",
        icon: "/placeholder-style.png",
      },
      {
        action: "close",
        title: "Close",
        icon: "/placeholder-style.png",
      },
    ],
  };

  _event.waitUntil(self.registration.showNotification("StyleGen AI", options));
});

// Notification click handling
self.addEventListener("notificationclick", (_event) => {
  _event.notification.close();

  if (_event.action === "explore") {
    _event.waitUntil(clients.openWindow("/"));
  }
});

// Defensive message event handler to prevent unhandled message channel errors
self.addEventListener("message", (_event) => {
  // Optionally respond to messages here if needed
  // _event.ports[0]?.postMessage({ status: 'received' });
});
