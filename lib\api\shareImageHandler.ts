import { createClient } from "@supabase/supabase-js";

export async function shareImageHandler({
  headers,
  body,
  env,
}: {
  headers: any;
  body: any;
  env: any;
}): Promise<{ status: number; body: any }> {
  try {
    const { imageId, isShared } = body;

    if (!imageId || typeof isShared !== "boolean") {
      return { status: 400, body: { error: "Missing or invalid parameters" } };
    }

    const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !serviceRoleKey) {
      return { status: 500, body: { error: "Server configuration error: Missing Supabase credentials" } };
    }

    const authHeader = headers["authorization"] || headers["Authorization"];
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { status: 401, body: { error: "Authentication required" } };
    }
    const token = authHeader.replace("Bearer ", "");

    // Inline initialization per request
    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: { autoRefreshToken: false, persistSession: false },
    });

    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return { status: 401, body: { error: "Invalid authentication token" } };
    }

    // Fetch the image to check ownership
    const { data: imageData, error: fetchError } = await supabase
      .from("images")
      .select("user_id")
      .eq("id", imageId)
      .single();

    if (fetchError) {
      return { status: 500, body: { error: `Database error: ${fetchError.message}` } };
    }

    if (!imageData) {
      return { status: 404, body: { error: "Image not found" } };
    }

    if (imageData.user_id !== user.id) {
      return { status: 403, body: { error: "Access denied: You can only modify your own images" } };
    }

    const update: any = { is_shared: isShared };
    if (isShared) {
      update.shared_at = new Date().toISOString();
    } else {
      update.shared_at = null;
    }

    const { error } = await supabase
      .from("images")
      .update(update)
      .eq("id", imageId);

    if (error) {
      return { status: 500, body: { error: error.message } };
    }

    return { status: 200, body: { success: true } };
  } catch (err) {
    console.error("Error in shareImageHandler:", err);
    return { status: 500, body: { error: "Server error" } };
  }
}
