import { NextRequest, NextResponse } from "next/server";
import { imageProxy<PERSON>andler } from "@/lib/api/imageProxyHandler";
import { headers } from "next/headers";

export async function GET(
  request: NextRequest,
  { params }: { params: { imageId: string } },
) {
  const requestHeaders = await headers();
  const { imageId } = params;
  const result = await imageProxyHandler({ imageId, headers: requestHeaders });
  if (result.headers) {
    // Binary/image response
    return new NextResponse(result.body, {
      status: result.status,
      headers: result.headers,
    });
  } else {
    // JSON error response
    return NextResponse.json(result.body, { status: result.status });
  }
}

// Handle HEAD requests for testing
export async function HEAD(
  request: NextRequest,
  { params }: { params: { imageId: string } },
) {
  try {
    const response = await GET(request, { params });

    if (response instanceof NextResponse && response.status === 200) {
      return new NextResponse(null, {
        status: 200,
        headers: response.headers,
      });
    }

    return response;
  } catch (error) {
    // console.error('❌ HEAD request error:', error); // Diagnostic
    return NextResponse.json({ error: "HEAD request failed" }, { status: 500 });
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers": "Authorization, Content-Type",
    },
  });
}
