import { NextRequest, NextResponse } from "next/server";
import { getSignedUrlHandler } from "@/lib/api/getSignedUrlHandler";

export async function POST(request: NextRequest) {
  const requestHeaders = Object.fromEntries(request.headers.entries());
  const body = await request.json();
  const env = process.env;
  const result = await getSignedUrlHandler({
    headers: requestHeaders,
    body,
    env,
  });
  return NextResponse.json(result.body, { status: result.status });
}
