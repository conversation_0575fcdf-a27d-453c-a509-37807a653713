import { renderHook, act } from '@testing-library/react-hooks';
import { useLikeToggle } from '../useLikeToggle';
import { apiService } from '@/lib/api/apiService';
import { errorHandler } from '@/lib/utils/errorHandler';

// Mock dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: { getSession: jest.fn(() => Promise.resolve({ data: { session: { access_token: 'token' } } })) },
  },
}));

describe('useLikeToggle', () => {
  it('should optimistically update like status and call API', async () => {
    const mutateLikeStatus = jest.fn();
    const { result } = renderHook(() =>
      useLikeToggle({
        imageId: 'img1',
        likeCount: 1,
        userLiked: false,
        mutateLikeStatus,
        isOwnImage: false,
        user: { id: 'user1' },
        isLoadingLikeStatus: false,
      })
    );
    await act(async () => {
      // Simulate click event
      await result.current.handleLikeToggle({ stopPropagation: () => {} } as any);
    });
    expect(mutateLikeStatus).toHaveBeenCalledWith({ like_count: 2, user_liked: true }, false);
    expect(mutateLikeStatus).toHaveBeenCalledWith({ like_count: 2, user_liked: true }, true);
  });

  it('should handle error and revert optimistic update', async () => {
    const mutateLikeStatus = jest.fn();
    // Force API to fail
    (apiService.post as jest.Mock).mockImplementationOnce(() => Promise.resolve({ data: null, error: 'API error' }));
    const { result } = renderHook(() =>
      useLikeToggle({
        imageId: 'img1',
        likeCount: 1,
        userLiked: false,
        mutateLikeStatus,
        isOwnImage: false,
        user: { id: 'user1' },
        isLoadingLikeStatus: false,
      })
    );
    await act(async () => {
      await result.current.handleLikeToggle({ stopPropagation: () => {} } as any);
    });
    // Should revert optimistic update
    expect(mutateLikeStatus).toHaveBeenCalledWith({ like_count: 1, user_liked: false }, false);
    // Should call errorHandler
    expect(errorHandler).toHaveBeenCalled();
  });
}); 