/** @type {import('next').NextConfig} */
// NOTE: The 'turbo' section is for Turbopack-specific options (currently empty, but future-proofed).
// The 'webpack' function below is ignored by Turbopack in dev mode, but still used for production builds.

import createBundleAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = createBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "oaidalleapiprodscus.blob.core.windows.net",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "expiprrhmxxcwyjueelg.supabase.co",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "*.supabase.co",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "xyz.supabase.co",
        port: "",
        pathname: "/**",
      },
    ],
  },
  serverExternalPackages: ["openai"],
  experimental: {
    optimizePackageImports: ["lucide-react"],
    allowedDevOrigins: ['3000-firebase-studio-1753291974031.cluster-64pjnskmlbaxowh5lzq6i7v4ra.cloudworkstations.dev', '6000-firebase-studio-1753291974031.cluster-64pjnskmlbaxowh5lzq6i7v4ra.cloudworkstations.dev', '9000-firebase-studio-1753291974031.cluster-64pjnskmlbaxowh5lzq6i7v4ra.cloudworkstations.dev']
  },
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
  reactStrictMode: true,

  turbopack: {
    // Turbopack-specific options will go here in the future
  },

  webpack: (config, { dev, isServer: _isServer, webpack }) => {
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: new RegExp('^\./locale$'),
        contextRegExp: /moment$/,
      }),
    );

    if (dev) {
      config.cache = false;
    }

    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
      stream: false,
      url: false,
      zlib: false,
      http: false,
      https: false,
      assert: false,
      os: false,
      path: false,
    };

    config.ignoreWarnings = [
      {
        module: /node_modules\/@supabase\/realtime-js/,
        message: /Critical dependency: the request of a dependency is an expression/,
      },
      {
        module: /node_modules\/node-fetch/,
        message: /Critical dependency/,
      },
      {
        module: /node_modules\/moment/,
        message: /Critical dependency/,
      },
      /Critical dependency: the request of a dependency is an expression/,
      /Module not found: Can't resolve/,
    ];

    return config;
  },

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Content-Security-Policy",
            // Updated CSP to allow Firebase, Google Analytics, Google Tag Manager, and required CDNs
            value:
              "default-src 'self'; " +
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://www.googletagmanager.com https://www.gstatic.com https://cdn.cloudflare.com https://va.vercel-scripts.com; " +
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
              "img-src 'self' data: blob: https://oaidalleapiprodscus.blob.core.windows.net https://*.supabase.co https://expiprrhmxxcwyjueelg.supabase.co https://www.googletagmanager.com https://www.gstatic.com https://cdn.jsdelivr.net https://cdn.cloudflare.com https://github.com https://avatars.githubusercontent.com; " +
              "font-src 'self' https://fonts.gstatic.com; " +
              "connect-src 'self' data: https://*.supabase.co https://api.openai.com https://firebase.googleapis.com https://firebaseinstallations.googleapis.com https://www.googleapis.com https://www.googletagmanager.com https://www.gstatic.com https://region1.google-analytics.com https://va.vercel-scripts.com; " +
              "frame-ancestors 'self' *.cloudworkstations.dev; object-src 'none'; base-uri 'self';",
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains",
          },
          // {
          //   key: 'Cross-Origin-Opener-Policy',
          //   value: 'same-origin',
          // },
          // {
          //   key: 'Cross-Origin-Embedder-Policy',
          //   value: 'require-corp',
          // },
          {
            key: "X-Robots-Tag",
            value: "index, follow",
          },
        ],
      },
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "Content-Type, Authorization",
          },
        ],
      },
    ];
  },

  staticPageGenerationTimeout: 1000,
  output: "standalone",
};

export default withBundleAnalyzer(nextConfig);
