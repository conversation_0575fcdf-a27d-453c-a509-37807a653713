-- Ensure update_like_count function exists
CREATE OR REPLACE FUNCTION public.update_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF (TG_OP = 'INSERT') THEN
        UPDATE public.images
        SET like_count = like_count + 1
        WHERE id = NEW.image_id;
    ELSIF (TG_OP = 'DELETE') THEN
        UPDATE public.images
        SET like_count = like_count - 1
        WHERE id = OLD.image_id;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Drop and recreate the trigger to ensure it's present and correct
DROP TRIGGER IF EXISTS likes_count_trigger ON public.likes;
CREATE TRIGGER likes_count_trigger
AFTER INSERT OR DELETE ON public.likes
FOR EACH ROW EXECUTE FUNCTION public.update_like_count();

-- Set like_count to NOT NULL and default 0
ALTER TABLE public.images
ALTER COLUMN like_count SET DEFAULT 0;

UPDATE public.images
SET like_count = 0
WHERE like_count IS NULL;

-- Recalculate like_count for all images
UPDATE public.images
SET like_count = (
  SELECT COUNT(*) FROM public.likes WHERE public.likes.image_id = public.images.id
); 