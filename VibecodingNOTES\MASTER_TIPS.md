# MASTER TIPS

## Fixing the Black Gap on the Scrollbar (Background Not Covering Scrollbar)

If you see a black (or blank) gap on the right side of your app, where the scrollbar appears outside your background, follow these steps to ensure your background covers the scrollbar area:

### 1. Set html and body to 100vw and min-height 100vh
Add this to your global CSS (e.g., `globals.css`):

```css
html, body {
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
}
```

### 2. Make sure your background layer covers the viewport
If you use a background component (e.g., `<AppBackground />`), ensure it uses:

```jsx
<div className="fixed inset-0 z-[-1] w-screen h-screen ...">
  {/* ... */}
</div>
```
- `fixed inset-0` makes it fill the viewport
- `z-[-1]` puts it behind all content
- `w-screen h-screen` ensures it covers the full width/height, including under the scrollbar

### 3. Avoid background colors on main content wrappers
Do not set a background color on your main grid or content wrappers, so the background layer is visible everywhere, including under the scrollbar.

---

**Result:** The background will extend under the scrollbar, eliminating the black gap and making the scrollbar appear over your background, even on Windows. 