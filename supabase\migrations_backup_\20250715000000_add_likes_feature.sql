-- Step 1: Create the likes table
CREATE TABLE public.likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    image_id UUID NOT NULL REFERENCES public.images(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT likes_image_id_user_id_key UNIQUE (image_id, user_id)
);

-- Step 2: Add like_count to images table
ALTER TABLE public.images
ADD COLUMN like_count INTEGER DEFAULT 0 NOT NULL;

-- Step 3: Create a function to update like_count on images table
CREATE OR REPLACE FUNCTION public.update_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF (TG_OP = 'INSERT') THEN
        UPDATE public.images
        SET like_count = like_count + 1
        WHERE id = NEW.image_id;
    ELSIF (TG_OP = 'DELETE') THEN
        UPDATE public.images
        SET like_count = like_count - 1
        WHERE id = OLD.image_id;
    END IF;
    RETURN NULL; -- result is ignored since this is an AFTER trigger
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Step 4: Create a trigger to call the function on insert/delete in likes table
CREATE TRIGGER likes_count_trigger
AFTER INSERT OR DELETE ON public.likes
FOR EACH ROW EXECUTE FUNCTION public.update_like_count();

-- Step 5: Add indexes to likes table
CREATE INDEX idx_likes_image_id ON public.likes(image_id);
CREATE INDEX idx_likes_user_id_image_id ON public.likes(user_id, image_id);

-- Step 6: Add RLS policies for likes table (initial setup, might need refinement)
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;

-- Users can insert their own likes
CREATE POLICY "Allow authenticated users to insert their own likes"
ON public.likes
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can delete their own likes
CREATE POLICY "Allow authenticated users to delete their own likes"
ON public.likes
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Allow all users (authenticated or anonymous) to read likes (e.g., for like counts or displaying who liked)
-- This might be too permissive depending on privacy requirements for "who liked",
-- This might be too permissive depending on privacy requirements for "who liked".
-- For aggregate counts, images.like_count is used. For a user's own like status, specific checks are made.
-- This policy restricts direct querying of the 'likes' table to a user's own like records.
CREATE POLICY "Allow authenticated users to select their own likes"
ON public.likes
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Optional: If we need to allow service_role to bypass RLS for specific operations (like admin tasks or triggers)
-- This is generally handled by the service_role key having bypass RLS capabilities by default.

-- Comment to explain the purpose of this migration
COMMENT ON TABLE public.likes IS 'Stores user likes for images.';
COMMENT ON COLUMN public.images.like_count IS 'Denormalized count of likes for an image.';
COMMENT ON FUNCTION public.update_like_count IS 'Updates the like_count on the images table when a like is added or removed.';
COMMENT ON TRIGGER likes_count_trigger ON public.likes IS 'Trigger to update like_count on images table after insert or delete on likes table.';

-- Grant usage on schema public to supabase_admin (if not already granted)
-- This is usually handled by Supabase default permissions.
-- GRANT USAGE ON SCHEMA public TO supabase_admin;
-- GRANT ALL ON TABLE public.likes TO supabase_admin;
-- GRANT ALL ON FUNCTION public.update_like_count() TO supabase_admin;

-- Grant usage to authenticated role for selecting from likes (if needed beyond RLS)
-- GRANT SELECT ON TABLE public.likes TO authenticated;

-- Note: The RLS policies for the `images` table should already allow public reads for `is_public=true`.
-- The `like_count` column will inherit the RLS of the `images` table.
-- We need to ensure `like_count` is selectable.
-- If `images` table has specific column select permissions, `like_count` should be added.
-- Assuming `images` allows select on all columns for public/authenticated users as appropriate.

-- Backfill existing images with like_count = 0 (already handled by DEFAULT 0)
-- If there were existing likes from another system, a backfill script would be needed here.
-- For this new feature, starting at 0 is correct.
