import { test, expect } from "@playwright/test";

test.describe("Community Page", () => {
  test("should render community header and filters", async ({ page }) => {
    await page.goto("/community");
    await expect(
      page.getByRole("heading", { name: "Community Gallery", level: 1 }),
    ).toBeVisible();
    await expect(page.locator("#community-style-filter")).toBeVisible();
    await expect(page.locator("#community-sort-order")).toBeVisible();
  });

  test("should show gallery grid or empty state", async ({ page }) => {
    await page.goto("/community");
    const grid = page.locator('[data-testid="community-gallery-grid"]');
    const empty = page.getByText("Community gallery is growing");
    if (await grid.isVisible({ timeout: 3000 })) {
      await expect(grid).toBeVisible();
    } else {
      await expect(empty).toBeVisible();
    }
  });

  test("should have correct SEO meta tags", async ({ page }) => {
    await page.goto("/community");
    await expect(page).toHaveTitle("PxlMorph AI - AI-Powered Image Generation");
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      "content",
      /Create stunning AI-generated images with advanced style controls and real-time editing capabilities\./i
    );
  });
});
