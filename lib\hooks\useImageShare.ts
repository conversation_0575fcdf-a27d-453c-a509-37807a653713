import { useCallback, useState } from "react";
import { showShareToast } from "@/lib/utils/toast";
import { errorHandler } from "@/lib/utils/errorHandler";

/**
 * useImageShare - Hook for sharing images via native share or clipboard, with toast feedback.
 * @returns {Object} { share, isSharing }
 */
export function useImageShare() {
  const [isSharing, setIsSharing] = useState(false);

  const share = useCallback(async (url: string, title?: string) => {
    setIsSharing(true);
    showShareToast("copied");
    try {
      if (navigator.share) {
        await navigator.share({ title: title || "PxlMorph AI", url });
      } else {
        await navigator.clipboard.writeText(url);
      }
    } catch (err) {
      errorHandler(err, { logOnly: true }); // Silently fail if share is cancelled
    } finally {
      setIsSharing(false);
    }
  }, []);

  return { share, isSharing };
}
