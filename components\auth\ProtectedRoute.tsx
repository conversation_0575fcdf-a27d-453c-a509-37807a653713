'use client';
import { useUser } from "@/lib/contexts/UserContext";
import UnifiedLoader from "@/components/ui/unified-loader";

export default function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useUser();

  if (isLoading) {
    // Show spinner or loader
    return <UnifiedLoader text="Loading..." />;
  }

  // If user is authenticated, render children
  if (user) {
    return <>{children}</>;
  }

  // If not authenticated, render nothing (middleware will have redirected)
  return null;
} 