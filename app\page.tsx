// Route: /
// Landing Page (public)
// This is the main public homepage for PxlMorph.
// Features branding, CTA, and highlights app features.
// No authentication required.
'use client';
export const dynamic = "force-dynamic";

import React, { Suspense } from 'react';
import { Motion } from '@/lib/utils/dynamicImports';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";
import { Wand2 } from "lucide-react";
import { createBrowserClient } from '@supabase/ssr';
import { useRouter } from "next/navigation";
import AppBackground from "@/components/layout/BackgroundWrapper";

// Type for feature items
interface Feature {
  icon: string;
  title: string;
  description: string;
}

const features: Feature[] = [
  {
    icon: '✨',
    title: 'AI-Powered',
    description: 'Cutting-edge AI technology'
  },
  {
    icon: '🎨',
    title: 'Endless Styles',
    description: 'Multiple art styles'
  },
  {
    icon: '⚡',
    title: 'Lightning Fast',
    description: 'Quick generations'
  }
];

export default function LandingPage() {
  const [isMounted, setIsMounted] = useState(false);
  const [user, setUser] = useState<{ id: string } | null>(null);
  const router = useRouter();

  useEffect(() => {
    setIsMounted(true);
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });
  }, []);

  if (!isMounted) return null;

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center overflow-hidden text-white">
      <AppBackground />
      <div className="relative z-10 w-full max-w-4xl p-6">
        {/* Animated Background */}
        <div className="pointer-events-none fixed inset-0 -z-10 overflow-hidden opacity-20">
          {Array.from({ length: 6 }).map((_, i) => (
            <Motion.div
              key={i}
              className="absolute rounded-full"
              style={{
                width: Math.random() * 300 + 100,
                height: Math.random() * 300 + 100,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                background: 'radial-gradient(circle, rgba(99,102,241,0.2) 0%, rgba(168,85,247,0) 70%)',
              }}
              animate={{
                x: [0, Math.random() * 60 - 30, 0],
                y: [0, Math.random() * 60 - 30, 0],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: Math.random() * 20 + 20,
                repeat: Infinity,
                repeatType: 'reverse' as const,
                ease: 'easeInOut',
              }}
            />
          ))}
        </div>
        
        {/* Logo/Brand */}
        <Motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="mb-10 flex items-center justify-center"
        >
          <div className="relative">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 opacity-30 blur-xl"></div>
            <div className="relative rounded-full bg-gradient-to-br from-slate-900 to-slate-800 p-2">
              <Wand2 className="size-10 text-white" />
            </div>
          </div>
          <h1 className="ml-3 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-2xl font-bold text-transparent">
            PxlMorph
          </h1>
          <style jsx global>{`
            @keyframes float {
              0% { transform: translateY(0); }
              50% { transform: translateY(-16px); }
              100% { transform: translateY(0); }
            }
            .animate-float {
              animation: float 3s ease-in-out infinite;
            }
            @keyframes gradient-move {
              0% { background-position: 0% 50%; }
              50% { background-position: 100% 50%; }
              100% { background-position: 0% 50%; }
            }
            .animate-gradient-move {
              background: linear-gradient(270deg, #7c3aed, #2563eb, #06b6d4, #7c3aed);
              background-size: 400% 400%;
              animation: gradient-move 6s ease-in-out infinite;
            }
          `}</style>
        </Motion.div>

        {/* Main Content */}
        <div className="mx-auto max-w-2xl">
          <div className="flex flex-col items-center gap-3 text-center">
            <Motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-2 text-4xl font-bold leading-tight text-white sm:text-5xl"
            >
              Transform Ideas into{' '}
              <span className="gradient-text">
                Stunning AI Art
              </span>
            </Motion.h1>

            <Motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mb-2 text-lg text-slate-300"
            >
              Create breathtaking visuals with AI-powered tools. No design skills needed.
            </Motion.p>

            <Motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mb-12 flex flex-col items-center justify-center"
            >
              <Button
                onClick={() => {
                  if (user) {
                    router.push("/studio");
                  } else {
                    router.push("/login");
                  }
                }}
                className="transform-btn-animated btn-glow hover-lift mx-auto flex min-h-[72px] min-w-[320px] items-center justify-center gap-4 rounded-2xl border-0 px-12 py-7 text-2xl font-bold text-white shadow-2xl transition-all duration-300 sm:text-3xl"
                aria-label="Start Creating Free"
              >
                <Wand2 className="-ml-2 mr-4 size-8" />
                Start Creating Free
              </Button>
            </Motion.div>
          </div>
        </div>

        {/* Features */}
        <Motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mx-auto mt-10 grid max-w-4xl grid-cols-1 gap-6 sm:grid-cols-3"
        >
          {features.map((feature, index) => (
            <Motion.div 
              key={index}
              className="to-white/3 flex flex-col items-center rounded-2xl border border-white/5 bg-gradient-to-br from-white/5 p-5 text-center backdrop-blur-sm transition-all duration-300 hover:border-white/10"
              whileHover={{ y: -5, boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}
            >
              <div className="mb-3 flex size-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 text-2xl">
                {feature.icon}
              </div>
              <h3 className="mb-1 text-lg font-semibold text-white">{feature.title}</h3>
              <p className="text-sm text-slate-400">{feature.description}</p>
            </Motion.div>
          ))}
        </Motion.div>
        
        {/* Footer */}
        <Motion.footer
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-8 text-center text-sm text-slate-400/80"
        >
          <Motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.65 }}
          >
            Trusted by creators worldwide
          </Motion.p>
          <Motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mt-2 flex justify-center space-x-4"
          >
             {['🎨', '📱', '🎭', '🖌️', '🎭'].map((emoji, i) => (
              <Motion.span
                key={i}
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.75 + i * 0.05 }}
                className="opacity-70 transition-opacity hover:opacity-100"
              >
                 {emoji}
              </Motion.span>
             ))}
          </Motion.div>
        </Motion.footer>
      </div>
    </div>
  );
}
