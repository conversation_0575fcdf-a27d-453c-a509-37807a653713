import { initializeApp } from "firebase/app";
import {
  getAnalytics,
  isSupported as analyticsSupported,
} from "firebase/analytics";
import { getPerformance } from "firebase/performance";

// Your Firebase configuration
// You'll need to replace these with your actual Firebase project config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Helper to check if IndexedDB is available
function isIndexedDBAvailable() {
  try {
    if (typeof window === "undefined" || !window.indexedDB) return false;
    return true;
  } catch (_e) {
    return false;
  }
}

// Initialize Analytics and export it
let analytics: any = null;
const perf: any = null;

// Only initialize analytics and performance on the client side and if supported
if (typeof window !== "undefined") {
  analyticsSupported().then((supported) => {
    if (supported) {
      try {
        analytics = getAnalytics(app);
      } catch (_e) {
        // Optionally log or handle analytics init error
        // console.warn('Analytics initialization failed:', _e);
      }
    }
  });
  if (isIndexedDBAvailable()) {
    try {
      // perf = getPerformance(app); // Temporarily disabled due to attribute value length issue
    } catch (_e) {
      // Optionally log or handle performance init error
      // console.warn('Performance initialization failed:', _e);
    }
  } else {
    // Optionally log: IndexedDB not available, skipping Performance Monitoring
    // console.info('IndexedDB not available, skipping Firebase Performance Monitoring.');
  }
}

export { app, analytics, perf };
