import useSWR from "swr";
import { SupabaseService } from "@/lib/supabase";
import { GeneratedImage } from "@/lib/types";

type SortByType = "recent" | "trending";

const fetchCommunityImages = async (_key: string, sortBy: SortByType, limit: number, offset: number) => {
  try {
    const images = await SupabaseService.getSharedImages(sortBy, limit, offset);
    return images;
  } catch (err) {
    console.error(
      "Error fetching community images, falling back to empty array",
      err,
    );
    return [];
  }
};

export function useCommunityImages(sortBy: SortByType = "recent", limit: number = 20, offset: number = 0) {
  const { data, error, isLoading, mutate } = useSWR<GeneratedImage[]>(
    ["community-images", sortBy, limit, offset],
    fetchCommunityImages,
  );
  return {
    images: data || [],
    isLoading,
    error,
    mutate,
    hasMore: (data && data.length === limit) || false,
  };
}
