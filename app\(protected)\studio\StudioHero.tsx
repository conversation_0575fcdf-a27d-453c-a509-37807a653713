import { Wand2 } from "lucide-react";
import React from "react";

export function StudioHero() {
  return (
    <div className="flex items-start gap-2 sm:gap-3">
      {/* Icon container */}
      <div className="size-8 shrink-0 sm:size-10 md:size-12">
        <div className="animate-gradient-move flex size-full items-center justify-center rounded-full border-2 border-white/20 bg-gradient-to-br from-violet-600 to-blue-500 p-1.5 sm:p-2">
          <Wand2 className="size-full text-white drop-shadow-xl" />
        </div>
      </div>
      
      {/* Text content */}
      <div>
        <h1 className="gradient-text text-xl font-bold leading-tight sm:text-2xl md:text-3xl lg:text-4xl">
          PxlMorph AI Studio
        </h1>
        <p className="mt-0.5 text-xs text-slate-300 sm:mt-1 sm:text-sm md:text-base">
          Transform any photo with AI
        </p>
      </div>
    </div>
  );
}
