/**
 * Enhanced Image Cache Service
 * Provides intelligent caching for images to improve loading performance
 */

interface CachedImage {
  url: string;
  blob: Blob;
  blobUrl?: string;
  timestamp: number;
  expiresAt: number;
}

interface CacheStats {
  totalSize: number;
  itemCount: number;
  hitRate: number;
  hits: number;
  misses: number;
}

// IndexedDB helper for persistent image blob cache
const IMAGE_DB_NAME = "ImageBlobCacheDB";
const IMAGE_STORE_NAME = "imageBlobs";
let imageDbPromise: Promise<IDBDatabase> | null = null;
const DEBUG_CACHE = false; // Set to true for verbose cache logs

// Add a persistent blob URL map for session-stable URLs
const persistentBlobUrlMap = new Map<string, string>();

function openImageDb(): Promise<IDBDatabase> {
  if (imageDbPromise) return imageDbPromise;
  imageDbPromise = new Promise((resolve, _reject) => {
    const request = indexedDB.open(IMAGE_DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(IMAGE_STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => _reject(request.error);
  });
  return imageDbPromise;
}

async function getBlobFromIndexedDb(key: string): Promise<Blob | undefined> {
  try {
    const db = await openImageDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(IMAGE_STORE_NAME, "readonly");
      const store = tx.objectStore(IMAGE_STORE_NAME);
      const req = store.get(key);
      req.onsuccess = () => {
        if (req.result) {
          resolve(req.result);
        } else {
          resolve(undefined);
        }
      };
      req.onerror = () => resolve(undefined);
    });
  } catch {
    return undefined;
  }
}

async function setBlobInIndexedDb(key: string, blob: Blob) {
  try {
    const db = await openImageDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(IMAGE_STORE_NAME, "readwrite");
      const store = tx.objectStore(IMAGE_STORE_NAME);
      const req = store.put(blob, key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch {
    // ignore
  }
}

// Concurrent image loading queue for smooth loading
const loadingQueue: Promise<any>[] = [];
const MAX_CONCURRENT_LOADS = 12; // Increased for faster loading

function enqueueLoad(fn: () => Promise<any>) {
  const run = async () => {
    try {
      return await fn();
    } finally {
      // Remove this promise from the queue when done
      const index = loadingQueue.findIndex((p) => p === promise);
      if (index > -1) {
        loadingQueue.splice(index, 1);
      }
    }
  };

  const promise = run();

  // Add to queue
  loadingQueue.push(promise);

  // If we have too many concurrent loads, wait for one to finish
  if (loadingQueue.length > MAX_CONCURRENT_LOADS) {
    return new Promise((resolve, reject) => {
      const checkQueue = () => {
        if (loadingQueue.length <= MAX_CONCURRENT_LOADS) {
          promise.then(resolve).catch(reject);
        } else {
          setTimeout(checkQueue, 10); // Check again in 10ms
        }
      };
      checkQueue();
    });
  }

  return promise;
}

class ImageCacheService {
  private cache = new Map<string, CachedImage>();
  private readonly maxCacheSize = 50 * 1024 * 1024; // 50MB max cache size
  private readonly maxItems = 100; // Max 100 cached images
  private readonly defaultTTL = 30 * 60 * 1000; // 30 minutes default TTL
  private stats: CacheStats = {
    totalSize: 0,
    itemCount: 0,
    hitRate: 0,
    hits: 0,
    misses: 0,
  };

  /**
   * Get an image from cache or fetch it if not cached
   */
  async getImage(url: string, authToken?: string): Promise<string> {
    const cacheKey = this.getCacheKey(url);
    // 1. Try persistent IndexedDB cache first
    const dbBlob = await getBlobFromIndexedDb(cacheKey);
    if (dbBlob) {
      this.stats.hits++;
      this.updateHitRate();
      if (DEBUG_CACHE)
        console.log(
          "\ud83c\udfaf Persistent cache HIT for:",
          url.substring(0, 50) + "...",
        );
      // Use a session-stable blob URL for persistent cache
      if (persistentBlobUrlMap.has(cacheKey)) {
        return persistentBlobUrlMap.get(cacheKey)!;
      } else {
        const blobUrl = URL.createObjectURL(dbBlob);
        if (DEBUG_CACHE)
          console.log(
            "[CACHE] Created new persistent blob URL for:",
            cacheKey,
            blobUrl,
          );
        persistentBlobUrlMap.set(cacheKey, blobUrl);
        return blobUrl;
      }
    }
    // 2. Try in-memory cache
    const cached = this.cache.get(cacheKey);
    if (cached && cached.expiresAt > Date.now()) {
      this.stats.hits++;
      this.updateHitRate();
      if (DEBUG_CACHE)
        console.log(
          "\ud83c\udfaf Cache HIT for:",
          url.substring(0, 50) + "...",
        );
      // Return the stable blob URL if it exists, otherwise create and store it
      if (!cached.blobUrl) {
        cached.blobUrl = URL.createObjectURL(cached.blob);
        if (DEBUG_CACHE)
          console.log(
            "[CACHE] Created new in-memory blob URL for:",
            cacheKey,
            cached.blobUrl,
          );
        this.cache.set(cacheKey, cached); // update cache entry
      }
      return cached.blobUrl;
    }
    // 3. Cache miss - fetch the image (with concurrency control)
    this.stats.misses++;
    this.updateHitRate();
    if (DEBUG_CACHE)
      console.log(
        "\ud83d\udce5 Cache MISS, fetching:",
        url.substring(0, 50) + "...",
      );
    const fetchAndCache = async () => {
      const headers: HeadersInit = {};
      if (authToken && (url.startsWith("/api/") || url.includes("supabase"))) {
        headers["Authorization"] = `Bearer ${authToken}`;
      }
      const response = await fetch(url, {
        headers,
        mode: "cors",
        credentials: "omit",
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }
      const blob = await response.blob();
      // Cache in memory
      await this.cacheImage(cacheKey, url, blob);
      // Cache in IndexedDB
      setBlobInIndexedDb(cacheKey, blob);
      console.log(
        "✅ Image fetched and cached (persistent):",
        url.substring(0, 50) + "...",
      );
      // After caching, get the stable blob URL from memory cache
      const cached = this.cache.get(cacheKey);
      if (cached) {
        if (!cached.blobUrl) {
          cached.blobUrl = URL.createObjectURL(cached.blob);
          if (DEBUG_CACHE)
            console.log(
              "[CACHE] Created new blob URL after fetch for:",
              cacheKey,
              cached.blobUrl,
            );
          this.cache.set(cacheKey, cached);
        }
        return cached.blobUrl;
      }
      // Fallback: create a blob URL (should not happen)
      const blobUrl = URL.createObjectURL(blob);
      if (DEBUG_CACHE)
        console.log(
          "[CACHE] Created fallback blob URL for:",
          cacheKey,
          blobUrl,
        );
      return blobUrl;
    };
    return enqueueLoad(fetchAndCache) as Promise<string>;
  }

  /**
   * Preload images for better performance
   */
  async preloadImages(urls: string[], authToken?: string): Promise<void> {
    if (DEBUG_CACHE)
      console.log("\ud83d\ude80 Preloading", urls.length, "images...");

    // Process in smaller batches to avoid overwhelming the network
    const batchSize = 6; // Optimal batch size for smooth loading
    const batches = [];

    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }

    // Process batches with minimal delays to maintain smooth loading
    for (const batch of batches) {
      const preloadPromises = batch.map((url) =>
        enqueueLoad(() => this.getImage(url, authToken)),
      );
      await Promise.allSettled(preloadPromises);

      // Very small delay between batches to prevent overwhelming
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 20));
      }
    }

    if (DEBUG_CACHE) console.log("\u2705 Image preloading completed");
  }

  /**
   * Cache an image blob
   */
  private async cacheImage(
    key: string,
    url: string,
    blob: Blob,
  ): Promise<void> {
    // Check if we need to make space
    if (
      this.cache.size >= this.maxItems ||
      this.stats.totalSize + blob.size > this.maxCacheSize
    ) {
      this.evictOldest();
    }
    // If an old entry exists, revoke its blob URL
    const old = this.cache.get(key);
    if (old && old.blobUrl) {
      URL.revokeObjectURL(old.blobUrl);
    }
    const blobUrl = URL.createObjectURL(blob);
    const cachedImage: CachedImage = {
      url,
      blob,
      blobUrl, // store the stable blob URL
      timestamp: Date.now(),
      expiresAt: Date.now() + this.defaultTTL,
    };
    this.cache.set(key, cachedImage);
    this.stats.totalSize += blob.size;
    this.stats.itemCount = this.cache.size;
    console.log("💾 Cached image:", {
      key: key.substring(0, 20) + "...",
      size: (blob.size / 1024).toFixed(1) + "KB",
      totalCached: this.stats.itemCount,
      totalSize: (this.stats.totalSize / 1024 / 1024).toFixed(1) + "MB",
    });
  }

  /**
   * Evict oldest cached images to make space
   */
  private evictOldest(): void {
    if (this.cache.size === 0) return;
    // Sort by timestamp and remove oldest
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    // Remove oldest 20% or at least 1 item
    const toRemove = Math.max(1, Math.floor(entries.length * 0.2));
    for (let i = 0; i < toRemove; i++) {
      const [key, cached] = entries[i];
      this.cache.delete(key);
      this.stats.totalSize -= cached.blob.size;
      // Revoke the blob URL to free memory
      if (cached.blobUrl) {
        URL.revokeObjectURL(cached.blobUrl);
      }
      // Revoke persistent blob URL if present
      if (persistentBlobUrlMap.has(key)) {
        URL.revokeObjectURL(persistentBlobUrlMap.get(key)!);
        persistentBlobUrlMap.delete(key);
      }
    }
    this.stats.itemCount = this.cache.size;
    console.log("🧹 Evicted", toRemove, "old cached images");
  }

  /**
   * Generate cache key from URL
   */
  private getCacheKey(url: string): string {
    // Remove query parameters and fragments for consistent caching
    try {
      const urlObj = new URL(url, window.location.origin);
      return urlObj.pathname;
    } catch {
      // Fallback for relative URLs
      return url.split("?")[0].split("#")[0];
    }
  }

  /**
   * Update hit rate statistics
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  /**
   * Clear expired items from cache
   */
  clearExpired(): void {
    const now = Date.now();
    let removedCount = 0;
    let removedSize = 0;
    for (const [key, cached] of this.cache.entries()) {
      if (cached.expiresAt <= now) {
        this.cache.delete(key);
        removedSize += cached.blob.size;
        removedCount++;
        // Revoke blob URL
        if (cached.blobUrl) {
          URL.revokeObjectURL(cached.blobUrl);
        }
        // Revoke persistent blob URL if present
        if (persistentBlobUrlMap.has(key)) {
          URL.revokeObjectURL(persistentBlobUrlMap.get(key)!);
          persistentBlobUrlMap.delete(key);
        }
      }
    }
    if (removedCount > 0) {
      this.stats.totalSize -= removedSize;
      this.stats.itemCount = this.cache.size;
      console.log("🧹 Cleared", removedCount, "expired cached images");
    }
  }

  /**
   * Clear all cached images
   */
  clearAll(): void {
    // Revoke all blob URLs
    for (const cached of this.cache.values()) {
      if (cached.blobUrl) {
        URL.revokeObjectURL(cached.blobUrl);
      }
    }
    // Revoke all persistent blob URLs
    for (const blobUrl of persistentBlobUrlMap.values()) {
      URL.revokeObjectURL(blobUrl);
    }
    persistentBlobUrlMap.clear();
    this.cache.clear();
    this.stats = {
      totalSize: 0,
      itemCount: 0,
      hitRate: 0,
      hits: 0,
      misses: 0,
    };
    console.log("🧹 Cleared all cached images");
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Check if an image is cached
   */
  isCached(url: string): boolean {
    const cacheKey = this.getCacheKey(url);
    const cached = this.cache.get(cacheKey);
    return cached ? cached.expiresAt > Date.now() : false;
  }

  /**
   * Warm up cache with commonly accessed images
   */
  async warmupCache(imageUrls: string[], authToken?: string): Promise<void> {
    console.log("🔥 Warming up cache with", imageUrls.length, "images...");

    // Preload in batches to avoid overwhelming the network
    const batchSize = 5;
    for (let i = 0; i < imageUrls.length; i += batchSize) {
      const batch = imageUrls.slice(i, i + batchSize);
      await this.preloadImages(batch, authToken);

      // Small delay between batches
      if (i + batchSize < imageUrls.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    console.log("✅ Cache warmup completed");
  }
}

// Export singleton instance
export const imageCache = new ImageCacheService();

// Auto-cleanup expired items every 5 minutes
if (typeof window !== "undefined") {
  setInterval(
    () => {
      imageCache.clearExpired();
    },
    5 * 60 * 1000,
  );
}
