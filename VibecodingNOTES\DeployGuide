## 1. **Code Quality & Linting**
- Run your linter (`eslint`, `tsc`, etc.) and fix any warnings or errors.
- Remove unused code, comments, and debug logs (e.g., `console.log`).

## 2. **Environment Variables**
- Ensure all required environment variables are set in your production environment (e.g., API keys, database URLs).
- Double-check `.env.production` or your deployment platform's secrets.

## 3. **Build Verification**
- Run a production build locally:  
  ```
  npm run build
  ```
  or for Next.js:
  ```
  npx next build
  ```
- Start the production server locally to verify:
  ```
  npm start
  ```
  or
  ```
  npx next start
  ```

## 4. **Static Assets**
- Confirm all static assets (images, icons, etc.) are optimized and present in the `public/` directory.
- Check for broken image links or missing files.

## 5. **SEO & Metadata**
- Ensure all pages have correct `<title>`, `<meta>` tags, and Open Graph/Twitter metadata.
- Check for canonical URLs and robots.txt if needed.

## 6. **Error Monitoring & Logging**
- Integrate error monitoring (e.g., Sentry, LogRocket) if not already done.
- Ensure server and client errors are logged and actionable.

## 7. **Performance**
- Run Lighthouse or similar audits for performance, accessibility, and best practices.
- Optimize images, code splitting, and lazy loading where possible.

## 8. **Security**
- ✅ Remove any test endpoints or debug routes.
- ✅ Ensure HTTPS is enforced (HSTS + HTTP-to-HTTPS redirect in middleware).
- ✅ Check for known vulnerabilities:
  ```
  npm audit
  ```
- ✅ Set appropriate HTTP headers (CSP, HSTS, etc.).
  - CSP and HSTS are now set in next.config.mjs
  - HTTP-to-HTTPS redirect is implemented in middleware.ts

## 9. **Database & API**
- Run database migrations (if any).
- Ensure production database is configured and not using test data.
- Test all critical API endpoints.

## 10. **Testing**
- All unit, integration, and E2E tests should pass.
- Optionally, run a final manual smoke test of key flows.

## 11. **Deployment Configuration**
- Set up your deployment platform (Vercel, Netlify, AWS, etc.) with correct build and start commands.
- Configure custom domains, SSL, and CDN as needed.

## 12. **Backups & Rollback**
- Ensure you have a backup/rollback plan for your database and deployment.

---

### Would you like to:
- Run a production build and start the server locally for a final check?
- Review your environment variable setup?
- Run a security audit (`npm audit`)?
- Or proceed directly to deployment instructions for your chosen platform?
