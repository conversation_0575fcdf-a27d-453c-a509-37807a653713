// Jest test file for OptimizedImage
// Requires: @testing-library/react, @testing-library/jest-dom, @types/jest
// Run: npm install --save-dev @testing-library/react @testing-library/jest-dom @types/jest

import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom";
import { OptimizedImage } from "../components/ui/optimized-image";
import userEvent from "@testing-library/user-event";

// Mock IntersectionObserver for jsdom
// @ts-ignore
global.IntersectionObserver = class {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

jest.mock("next/image", () => {
  return {
    __esModule: true,
    default: (props: any) => {
      const { priority, fill, ...rest } = props;
      return require("react").createElement("img", rest);
    },
  };
});

describe("OptimizedImage", () => {
  const baseProps = {
    src: "/test-image.jpg",
    alt: "Test image",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("renders with basic props", () => {
      render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute("src", "/test-image.jpg");
    });

    it("renders with custom dimensions", () => {
      render(<OptimizedImage {...baseProps} width={800} height={600} />);

      const image = screen.getByAltText("Test image");
      expect(image).toHaveAttribute("width", "800");
      expect(image).toHaveAttribute("height", "600");
    });

    it("renders with custom className", () => {
      render(<OptimizedImage {...baseProps} className="custom-class" />);

      const container = screen
        .getByAltText("Test image")
        .closest(".custom-class");
      expect(container).toBeInTheDocument();
    });

    it("renders with priority loading", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} priority />);
      });
      const image = screen.getByAltText("Test image");
      expect(image).toBeInTheDocument();
    });

    it("renders with custom quality", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} quality={95} />);
      });
      const image = screen.getByAltText("Test image");
      expect(image).toHaveAttribute("quality", "95");
    });

    it("renders with blur placeholder", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} placeholder="blur" />);
      });
      const image = screen.getByAltText("Test image");
      expect(image).toHaveAttribute("placeholder", "blur");
    });

    it("renders with custom sizes", async () => {
      const customSizes = "(max-width: 768px) 100vw, 50vw";
      await act(async () => {
        render(<OptimizedImage {...baseProps} sizes={customSizes} />);
      });
      const image = screen.getByAltText("Test image");
      expect(image).toHaveAttribute("sizes", customSizes);
    });

    it("renders with eager loading", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} loading="eager" />);
      });
      const image = screen.getByAltText("Test image");
      expect(image).toHaveAttribute("loading", "eager");
    });
  });

  describe("Loading States", () => {
    it("shows loading skeleton initially", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} />);
      });
      const skeleton = document.querySelector(".animate-pulse");
      expect(skeleton).toBeInTheDocument();
    });

    it("hides loading skeleton after image loads", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} />);
      });
      const image = screen.getByAltText("Test image");
      fireEvent.load(image);
      await waitFor(() => {
        const skeleton = document.querySelector(".animate-pulse");
        expect(skeleton).not.toBeInTheDocument();
      });
    });

    it("calls onLoad callback when image loads", async () => {
      const mockOnLoad = jest.fn();
      await act(async () => {
        render(<OptimizedImage {...baseProps} onLoad={mockOnLoad} />);
      });
      const image = screen.getByAltText("Test image");
      fireEvent.load(image);
      expect(mockOnLoad).toHaveBeenCalledTimes(1);
    });
  });

  describe("Error Handling", () => {
    it("calls onError callback when image fails to load", async () => {
      const mockOnError = jest.fn();
      await act(async () => {
        render(<OptimizedImage {...baseProps} onError={mockOnError} />);
      });
      const image = screen.getByAltText("Test image");
      fireEvent.error(image);
      expect(mockOnError).toHaveBeenCalledTimes(1);
    });

    it("shows fallback image when error occurs and fallbackSrc is provided", async () => {
      const fallbackSrc = "/fallback-image.jpg";
      await act(async () => {
        render(<OptimizedImage {...baseProps} fallbackSrc={fallbackSrc} />);
      });
      const image = screen.getByAltText("Test image");
      fireEvent.error(image);
      await waitFor(() => {
        const fallbackImage = screen.getByAltText("Test image");
        expect(fallbackImage).toHaveAttribute("src", fallbackSrc);
      });
    });

    it("shows error state when image fails and no fallback is provided", async () => {
      await act(async () => {
        render(<OptimizedImage {...baseProps} />);
      });
      const image = screen.getByAltText("Test image");
      fireEvent.error(image);
      await waitFor(() => {
        expect(screen.getByText("🖼️")).toBeInTheDocument();
        expect(screen.getByText("Image unavailable")).toBeInTheDocument();
      });
    });

    it("resets error state when src changes", async () => {
      let rerender: (ui: React.ReactElement) => void;
      await act(async () => {
        ({ rerender } = render(<OptimizedImage {...baseProps} />));
      });
      const image = screen.getByAltText("Test image");
      fireEvent.error(image);
      await waitFor(() => {
        expect(screen.getByText("Image unavailable")).toBeInTheDocument();
      });
      // Change src
      await act(async () => {
        rerender(<OptimizedImage {...baseProps} src="/new-image.jpg" />);
      });
      await waitFor(() => {
        expect(screen.queryByText("Image unavailable")).not.toBeInTheDocument();
        const newImage = screen.getByAltText("Test image");
        expect(newImage).toHaveAttribute("src", "/new-image.jpg");
      });
    });
  });

  describe("Image Styling", () => {
    it("applies correct CSS classes for loading state", () => {
      render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      expect(image).toHaveClass("opacity-0");
    });

    it("applies correct CSS classes after loading", async () => {
      render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      fireEvent.load(image);

      await waitFor(() => {
        expect(image).toHaveClass("opacity-100");
      });
    });

    it("applies the correct class to the image", () => {
      render(<OptimizedImage src="test.jpg" alt="test" />);
      const img = screen.getByAltText("test");
      // Check for a class or attribute that OptimizedImage applies
      expect(img).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has correct alt text", () => {
      render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      expect(image).toBeInTheDocument();
    });

    it("has correct alt text for error state", async () => {
      render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      fireEvent.error(image);

      await waitFor(() => {
        expect(screen.getByText("Image unavailable")).toBeInTheDocument();
      });
    });
  });

  describe("Performance", () => {
    it("uses memo to prevent unnecessary re-renders", () => {
      const { rerender } = render(<OptimizedImage {...baseProps} />);

      const image = screen.getByAltText("Test image");
      const initialSrc = image.getAttribute("src");

      // Re-render with same props
      rerender(<OptimizedImage {...baseProps} />);

      const newImage = screen.getByAltText("Test image");
      expect(newImage.getAttribute("src")).toBe(initialSrc);
    });
  });
});
