"use client";

import { useEffect, useRef } from "react";
import { GeneratedImage } from "@/lib/types";
import { ImageGrid } from "@/components/gallery/ImageGrid";

// Define CommunityGalleryGridProps interface for props
interface CommunityGalleryGridProps {
  filteredImages: Array<GeneratedImage>;
  isLoading: boolean;
  error: string | null;
  onShare?: (imageId: string, isShared: boolean) => void;
  isImageOwner: (image: GeneratedImage) => boolean;
  loadMore: () => void;
  hasMore: boolean;
}

export function CommunityGalleryGrid({
  filteredImages,
  isLoading,
  error,
  onShare,
  isImageOwner,
  loadMore,
  hasMore,
}: CommunityGalleryGridProps) {
  const loaderRef = useRef<HTMLDivElement>(null);

  // Infinite scroll: observe the loader div
  useEffect(() => {
    const currentLoader = loaderRef.current;
    if (!loadMore || !hasMore || isLoading) return;
    const observer = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading && hasMore) {
          loadMore();
        }
      },
      { rootMargin: "200px" }
    );
    if (currentLoader) observer.observe(currentLoader);
    return () => {
      if (currentLoader) observer.unobserve(currentLoader);
    };
  }, [loadMore, hasMore, isLoading]);

  if (error) {
    return (
      <div className="flex items-center justify-center py-12 text-red-500">
        Failed to load community gallery.
      </div>
    );
  }
  // Deduplicate images by id
  const dedupedImages = Array.from(
    new Map(filteredImages.map((img) => [img.id, img])).values(),
  );
  return (
    <ImageGrid
      images={dedupedImages}
      isLoading={isLoading}
      error={error}
      loadMore={loadMore}
      hasMore={hasMore}
      onShare={(image: GeneratedImage) => (isImageOwner(image) ? ((imageId: string, isShared: boolean) => onShare?.(imageId, isShared)) : undefined)}
      showDownloadButton={false}
      showSharedChip={(image: GeneratedImage) => isImageOwner(image)}
      hideLikeButton={(image: GeneratedImage) => isImageOwner(image)}
      cardVariant="default"
    />
  );
}
