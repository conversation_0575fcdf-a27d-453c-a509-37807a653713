import { GeneratedImage, User } from "./types";
import { CACHE_KEYS } from "./constants";
import { toast } from "sonner";
import { showCacheWarningToast } from "@/lib/utils/toast";

let cacheWarningShown = false;

function handleCacheError(error: any, operation: string) {
  console.debug(`CacheService: ${operation} failed.`, error);
  if (!cacheWarningShown && typeof window !== "undefined") {
    showCacheWarningToast();
    cacheWarningShown = true;
  }
}

// IndexedDB helper for persistent cache
const CACHE_DB_NAME = "AppCacheDB";
const CACHE_STORE_NAME = "cacheStore";
let cacheDbPromise: Promise<IDBDatabase> | null = null;

function openCacheDb(): Promise<IDBDatabase> {
  if (cacheDbPromise) return cacheDbPromise;
  cacheDbPromise = new Promise((resolve, _reject) => {
    const request = indexedDB.open(CACHE_DB_NAME, 1);
    request.onupgradeneeded = () => {
      request.result.createObjectStore(CACHE_STORE_NAME);
    };
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => _reject(request.error);
  });
  return cacheDbPromise;
}

async function setCacheItem(key: string, value: any) {
  try {
    const db = await openCacheDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(CACHE_STORE_NAME, "readwrite");
      const store = tx.objectStore(CACHE_STORE_NAME);
      const req = store.put(value, key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch (error) {
    handleCacheError(error, "setCacheItem");
  }
}

async function getCacheItem<T>(key: string): Promise<T | null> {
  try {
    const db = await openCacheDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(CACHE_STORE_NAME, "readonly");
      const store = tx.objectStore(CACHE_STORE_NAME);
      const req = store.get(key);
      req.onsuccess = () => {
        if (req.result) {
          resolve(req.result);
        } else {
          resolve(null);
        }
      };
      req.onerror = () => resolve(null);
    });
  } catch {
    return null;
  }
}

async function removeCacheItem(key: string) {
  try {
    const db = await openCacheDb();
    return new Promise((resolve, _reject) => {
      const tx = db.transaction(CACHE_STORE_NAME, "readwrite");
      const store = tx.objectStore(CACHE_STORE_NAME);
      const req = store.delete(key);
      req.onsuccess = () => resolve(undefined);
      req.onerror = () => resolve(undefined);
    });
  } catch (error) {
    handleCacheError(error, "removeCacheItem");
  }
}

export class CacheService {
  static isOnline(): boolean {
    if (typeof navigator === "undefined") return true;
    return navigator.onLine;
  }

  static async cacheGeneratedImages(images: GeneratedImage[]): Promise<void> {
    if (typeof window === "undefined") return;
    try {
      await setCacheItem(CACHE_KEYS.GENERATED_IMAGES, images);
    } catch (error) {
      handleCacheError(error, "cacheGeneratedImages");
    }
  }

  static async getCachedImages(): Promise<GeneratedImage[]> {
    if (typeof window === "undefined") return [];
    try {
      const cached = await getCacheItem<GeneratedImage[]>(
        CACHE_KEYS.GENERATED_IMAGES,
      );
      return cached || [];
    } catch (error) {
      console.debug("Cache retrieval failed:", error);
      return [];
    }
  }

  static async cacheUserProfile(user: User): Promise<void> {
    if (typeof window === "undefined") return;
    try {
      await setCacheItem(CACHE_KEYS.USER_PROFILE, user);
    } catch (error) {
      handleCacheError(error, "cacheUserProfile");
    }
  }

  static async getCachedUserProfile(): Promise<User | null> {
    if (typeof window === "undefined") return null;
    try {
      const cached = await getCacheItem<User>(CACHE_KEYS.USER_PROFILE);
      return cached || null;
    } catch (error) {
      console.debug("User profile retrieval failed:", error);
      return null;
    }
  }

  static addToRecentCreations(image: GeneratedImage): void {
    if (typeof window === "undefined") return;

    this.getCachedImages()
      .then((cached) => {
        const updated = [image, ...cached.filter((img) => img.id !== image.id)];
        this.cacheGeneratedImages(updated);
      })
      .catch((error) => {
        console.debug("Recent creations update failed:", error);
      });
  }

  static async clearCache(): Promise<void> {
    if (typeof window === "undefined") return;
    try {
      await removeCacheItem(CACHE_KEYS.GENERATED_IMAGES);
      await removeCacheItem(CACHE_KEYS.USER_PROFILE);
    } catch (error) {
      handleCacheError(error, "clearCache");
    }
  }

  static async cacheCommunityImages(images: GeneratedImage[]): Promise<void> {
    if (typeof window === "undefined") return;
    try {
      await setCacheItem(CACHE_KEYS.COMMUNITY_IMAGES, images);
    } catch (error) {
      handleCacheError(error, "cacheCommunityImages");
    }
  }

  static async getCachedCommunityImages(): Promise<GeneratedImage[]> {
    if (typeof window === "undefined") return [];
    try {
      const cached = await getCacheItem<GeneratedImage[]>(
        CACHE_KEYS.COMMUNITY_IMAGES,
      );
      return cached || [];
    } catch (error) {
      console.debug("Community cache retrieval failed:", error);
      return [];
    }
  }
}
