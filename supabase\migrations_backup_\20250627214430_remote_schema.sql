alter table "public"."user_favorites" drop constraint "user_favorites_user_id_fkey";

alter table "public"."images" add column "blurdataurl" text;

alter table "public"."images" add column "original_dalle_url" text;

alter table "public"."user_favorites" add constraint "user_favorites_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."user_favorites" validate constraint "user_favorites_user_id_fkey";


