import { NextRequest, NextResponse } from "next/server";
import { shareImageHandler } from "@/lib/api/shareImageHandler";

export async function POST(request: NextRequest) {
  const requestHeaders = Object.fromEntries(request.headers.entries());
  const body = await request.json();
  const env = process.env;
  const { status, body: responseBody } = await shareImageHandler({
    headers: requestHeaders,
    body,
    env,
  });
  return NextResponse.json(responseBody, { status });
}
