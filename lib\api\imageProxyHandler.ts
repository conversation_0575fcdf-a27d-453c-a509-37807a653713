import { createClient } from "@supabase/supabase-js";

export async function imageProxyHandler({
  imageId,
  headers: requestHeaders,
}: {
  imageId: string;
  headers: Headers;
}): Promise<{ status: number; body: any; headers?: Record<string, string> }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceKey) {
      return {
        status: 500,
        body: {
          error: "Server configuration error: Missing Supabase credentials",
        },
      };
    }
    const authHeader = requestHeaders.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { status: 401, body: { error: "Authentication required" } };
    }
    const token = authHeader.replace("Bearer ", "");
    const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
      auth: { autoRefreshToken: false, persistSession: false },
    });
    const {
      data: { user },
      error: authError,
    } = await supabaseServer.auth.getUser(token);
    if (authError || !user) {
      return { status: 401, body: { error: "Invalid authentication token" } };
    }
    // Get image metadata
    const { data: imageData, error: dbError } = await supabaseServer
      .from("images")
      .select("image_url, user_id, is_shared")
      .eq("id", imageId)
      .single();
    if (dbError) {
      return {
        status: dbError.code === "PGRST116" ? 404 : 500,
        body: { error: `Database error: ${dbError.message}` },
      };
    }
    if (!imageData) {
      return { status: 404, body: { error: "Image not found" } };
    }
    const hasAccess = imageData.user_id === user.id || imageData.is_shared;
    if (!hasAccess) {
      return {
        status: 403,
        body: {
          error:
            "Access denied: You do not own this image and it is not shared",
        },
      };
    }
    const storagePathStr = String(imageData.image_url).trim();
    let filePath: string | undefined = undefined;
    let isDalle = false;
    try {
      const parsedUrl = new URL(storagePathStr);
      if (parsedUrl.hostname.includes("blob.core.windows.net")) {
        isDalle = true;
      }
    } catch (_e) {
      isDalle = false;
    }
    if (storagePathStr.startsWith("private-images/")) {
      filePath = storagePathStr.replace("private-images/", "");
    } else if (storagePathStr.includes("/storage/v1/object/")) {
      const url = new URL(storagePathStr);
      const pathParts = url.pathname.split("/");
      const bucketIndex = pathParts.findIndex(
        (part) => part === "private-images",
      );
      if (bucketIndex === -1) {
        return { status: 400, body: { error: "Invalid image path format" } };
      }
      filePath = pathParts.slice(bucketIndex + 1).join("/");
    } else if (isDalle) {
      try {
        const dalleResponse = await fetch(storagePathStr);
        if (!dalleResponse.ok) {
          throw new Error(
            `DALL-E fetch failed: ${dalleResponse.status} ${dalleResponse.statusText}`,
          );
        }
        const imageBuffer = await dalleResponse.arrayBuffer();
        return {
          status: 200,
          body: Buffer.from(imageBuffer),
          headers: {
            "Content-Type":
              dalleResponse.headers.get("content-type") || "image/png",
            "Content-Length": imageBuffer.byteLength.toString(),
            "Cache-Control": "public, max-age=31536000, immutable",
            "Access-Control-Allow-Origin": "*",
          },
        };
      } catch (dalleError: any) {
        return {
          status: 500,
          body: { error: `Failed to proxy DALL-E image: ${dalleError}` },
        };
      }
    } else if (storagePathStr.startsWith("http")) {
      try {
        const externalResponse = await fetch(storagePathStr);
        if (!externalResponse.ok) {
          throw new Error(
            `External fetch failed: ${externalResponse.status} ${externalResponse.statusText}`,
          );
        }
        const imageBuffer = await externalResponse.arrayBuffer();
        return {
          status: 200,
          body: Buffer.from(imageBuffer),
          headers: {
            "Content-Type":
              externalResponse.headers.get("content-type") || "image/png",
            "Content-Length": imageBuffer.byteLength.toString(),
            "Cache-Control": "public, max-age=31536000, immutable",
            "Access-Control-Allow-Origin": "*",
          },
        };
      } catch (externalError: any) {
        return {
          status: 500,
          body: { error: `Failed to proxy external image: ${externalError}` },
        };
      }
    }
    // If filePath is still undefined, return error
    if (!filePath) {
      return { status: 400, body: { error: "Unknown image URL format" } };
    }
    // Download from Supabase Storage
    const { data: fileData, error: downloadError } =
      await supabaseServer.storage.from("private-images").download(filePath);
    if (downloadError) {
      return {
        status: 500,
        body: { error: `Storage error: ${downloadError.message}` },
      };
    }
    if (!fileData) {
      return { status: 404, body: { error: "File not found in storage" } };
    }
    const arrayBuffer = await fileData.arrayBuffer();
    return {
      status: 200,
      body: Buffer.from(arrayBuffer),
      headers: {
        "Content-Type": fileData.type || "image/png",
        "Content-Length": arrayBuffer.byteLength.toString(),
        "Cache-Control": "public, max-age=31536000, immutable",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
        "Access-Control-Allow-Headers": "Authorization, Content-Type",
        "X-Image-ID": imageId,
        "X-User-ID": user.id,
        "X-File-Path": filePath,
      },
    };
  } catch (error: any) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    if (
      errorMessage.includes("requestAsyncStorage") ||
      errorMessage.includes("headers not available")
    ) {
      return {
        status: 500,
        body: {
          error:
            "Server context error. The request was not processed in the correct context.",
        },
      };
    }
    return {
      status: 500,
      body: { error: `Image proxy failed: ${errorMessage}` },
    };
  }
}
