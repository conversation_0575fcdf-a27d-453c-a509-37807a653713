// @ts-nocheck
import {
  getUser<PERSON>avori<PERSON><PERSON><PERSON><PERSON>,
  postUserFavorites<PERSON>andler,
} from "@/lib/api/userFavoritesHandler";
import { Buffer } from "buffer";

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock Supabase
const mockGetUser = jest.fn(() =>
  Promise.resolve({ data: { user: { id: "user123" } }, error: null }),
);
const mockSelect = jest.fn(() => ({ eq: jest.fn().mockReturnThis() }));
const mockFrom = jest.fn(() => ({
  select: mockSelect,
  eq: jest.fn().mockReturnThis(),
  insert: jest.fn(() => Promise.resolve({ error: null })),
  delete: jest.fn(() => ({ eq: jest.fn().mockReturnThis() })),
}));
const mockCreateClient = () => ({
  auth: { getUser: mockGetUser },
  from: mockFrom,
});

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("userFavoritesHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("GET returns 401 if no auth header", async () => {
    const headers = new Headers();
    const { status, body } = await getUserFavoritesHandler({ headers });
    expect(status).toBe(401);
    expect(body.error).toMatch(/Unauthorized/);
  });

  it("POST returns 401 if no auth header", async () => {
    const headers = new Headers();
    const { status, body } = await postUserFavoritesHandler({
      headers,
      styleId: "style1",
      action: "add",
    });
    expect(status).toBe(401);
    expect(body.error).toMatch(/Unauthorized/);
  });

  it("POST returns 400 if styleId or action is missing", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await postUserFavoritesHandler({
      headers,
      styleId: "",
      action: "",
    });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Missing styleId or action/);
  });

  it("POST returns 400 if action is invalid", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await postUserFavoritesHandler({
      headers,
      styleId: "style1",
      action: "invalid",
    });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Invalid action/);
  });

  it("POST returns 200 on add success", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await postUserFavoritesHandler({
      headers,
      styleId: "style1",
      action: "add",
    });
    expect(status).toBe(200);
    expect(body.success).toBe(true);
    expect(body.action).toBe("add");
    expect(body.styleId).toBe("style1");
  });
});
