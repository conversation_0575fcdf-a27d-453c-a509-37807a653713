import { test, expect } from "@playwright/test";

test.describe("Not Found Page", () => {
  test("should render 404 message", async ({ page }) => {
    await page.goto("/not-found");
    await expect(page.getByText(/404 - Page Not Found/i)).toBeVisible();
    await expect(
      page.getByText(/Sorry, the page you are looking for does not exist/i),
    ).toBeVisible();
  });

  test("should have correct SEO meta tags", async ({ page }) => {
    await page.goto("/not-found");
    await expect(page).toHaveTitle("PxlMorph AI - AI-Powered Image Generation");
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      "content",
      /Create stunning AI-generated images with advanced style controls and real-time editing capabilities\./i
    );
  });
});
