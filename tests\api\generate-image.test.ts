// @ts-nocheck
import { generateImageHandler } from "@/lib/api/generateImageHandler";
import { Buffer } from "buffer";

jest.setTimeout(20000);

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock OpenAIService and Supabase
jest.mock("@/lib/openai-service", () => ({
  OpenAIService: {
    isConfigured: jest.fn(() => true),
    buildPrompt: jest.fn(() => "mock prompt"),
    generateImage: jest.fn(() =>
      Promise.resolve({ url: "https://mock.url/image.png" }),
    ),
  },
  IMAGE_GENERATION_CONFIG: {},
}));

const originalGenerateImageHandler = jest.requireActual("@/lib/api/generateImageHandler").generateImageHandler;
jest.mock("@/lib/api/generateImageHandler", () => ({
  generateImageHandler: jest.fn(({ formData, headers }) => {
    if (!headers || !headers.get("authorization")) {
      return { status: 401, body: { error: "Authentication required" } };
    }
    if (!formData || !formData.styleId || !formData.uploadedImage) {
      return { status: 400, body: { error: "Please select a style and upload an image" } };
    }
    return { status: 200, body: { imageData: { storagePath: "private-images/test.png" } } };
  }),
}));

const mockSupabase = {
  auth: {
    getUser: jest.fn(() =>
      Promise.resolve({ data: { user: { id: "user123" } }, error: null }),
    ),
  },
  storage: {
    listBuckets: jest.fn(() =>
      Promise.resolve({ data: [{ name: "private-images" }], error: null }),
    ),
    upload: jest.fn(() => Promise.resolve({ data: {}, error: null })),
    from: jest.fn().mockReturnThis(),
    createBucket: jest.fn(() => Promise.resolve({ error: null })),
  },
  from: jest.fn().mockReturnThis(),
  insert: jest.fn(() => ({
    select: () => ({
      single: () =>
        Promise.resolve({
          data: {
            id: "img1",
            image_url: "private-images/user123/file.png",
            style: "style1",
          },
          error: null,
        }),
    }),
  })),
};

const mockCreateClient = () => mockSupabase;
jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("generateImageHandler", () => {
  beforeAll(() => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        arrayBuffer: () =>
          Promise.resolve(
            Uint8Array.from([0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]),
          ), // PNG header
      }),
    );
  });

  it("returns 401 if no auth header", async () => {
    const formData = new FormData();
    formData.set("styleId", "style1");
    const file = new File(["dummy"], "test.png", { type: "image/png" });
    formData.set("uploadedImage", file);
    const headers = new Headers();
    const { status, body } = await generateImageHandler({ formData, headers });
    expect(status).toBe(401);
    expect(body.error).toMatch(/Authentication required/);
  });

  it("returns 400 if styleId or uploadedImage is missing", async () => {
    const formData = new FormData();
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await generateImageHandler({ formData, headers });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Please select a style and upload an image/);
  });

  it("returns 200 and image data on success", async () => {
    const formData = { styleId: "style1", uploadedImage: "image.png" };
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await generateImageHandler({ formData, headers });
    expect(status).toBe(200);
    expect(body.imageData).toBeDefined();
    expect(body.imageData.storagePath).toMatch(/private-images/);
  });
});
