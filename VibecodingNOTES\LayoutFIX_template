# Layout Structure Consistency Template

## Overview
This template provides a systematic approach to identify and fix layout inconsistencies in a Next.js 13+ App Router application with a unified layout system.

## Layout System Architecture

### ✅ **Correct Unified Layout Structure**
```tsx
// Root Layout (app/layout.tsx)
<div id="app-root" className="relative min-h-screen w-full flex font-sans antialiased z-0">
  <Navbar /> {/* Desktop sticky navbar */}
  <main className="flex-1 flex flex-col min-h-0 w-full overflow-hidden">
    {children}
  </main>
</div>
<MobileBottomNav /> {/* Mobile bottom navigation */}

// Protected Layout (app/(protected)/layout.tsx)
<div className="flex-1 flex flex-col min-h-0 w-full p-6 pb-24 md:pb-6">
  {children}
</div>
```

## �� **Layout Issues to Check For**

### 1. **Height Calculation Issues**
**❌ Problematic Patterns:**
```tsx
// OLD - Don't use these
min-h-[calc(100vh-4rem)]
max-h-[calc(100vh-48px)]
h-[calc(100vh-60px)]
```

**✅ Correct Patterns:**
```tsx
// NEW - Use these instead
min-h-screen
h-full
max-h-full
```

### 2. **Page-Level Layout Issues**
**❌ Problematic Patterns:**
```tsx
// OLD - Don't use these in page components
<div className="min-h-screen md:pl-20 flex items-center justify-center">
<div className="min-h-[calc(100vh-4rem)] w-full">
```

**✅ Correct Patterns:**
```tsx
// NEW - Use these in page components
<div className="flex items-center justify-center w-full p-6 pb-24 md:pb-6">
<div className="w-full">
```

### 3. **Component-Level Layout Issues**
**❌ Problematic Patterns:**
```tsx
// OLD - Don't use these in components
<div className="min-h-screen flex items-center justify-center">
<div className="flex min-h-screen items-center justify-center">
```

**✅ Correct Patterns:**
```tsx
// NEW - Use these in components
<div className="flex items-center justify-center">
<div className="w-full">
```

### 4. **Navigation Layout Issues**
**❌ Problematic Patterns:**
```tsx
// OLD - Don't use these
grid grid-cols-[auto_1fr]
md:pl-20
```

**✅ Correct Patterns:**
```tsx
// NEW - Use these
flex flex-col lg:flex-row
flex-1 flex flex-col
```

## 🔧 **Systematic Fix Process**

### Step 1: Search for Problematic Patterns
```bash
# Search for old height calculations
grep -r "min-h-\[calc\(100vh" .
grep -r "max-h-\[calc\(100vh" .
grep -r "h-\[calc\(100vh" .

# Search for old layout patterns
grep -r "md:pl-" .
grep -r "grid grid-cols-\[auto_1fr\]" .
grep -r "min-h-screen.*md:pl" .

# Search for problematic component patterns
grep -r "min-h-screen.*flex.*items-center" .
grep -r "flex.*min-h-screen.*items-center" .
```

### Step 2: Fix Page Components
**For each page component:**

1. **Remove old height calculations**
2. **Use unified padding system**
3. **Remove conflicting layout wrappers**

```tsx
// BEFORE (❌)
export default function SomePage() {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] w-full bg-transparent px-2 sm:px-0">
      <div className="p-4">
        <SomeComponent />
      </div>
    </div>
  );
}

// AFTER (✅)
export default function SomePage() {
  return (
    <div className="flex items-center justify-center w-full p-6 pb-24 md:pb-6">
      <SomeComponent />
    </div>
  );
}
```

### Step 3: Fix Component Loading States
**For components with loading states:**

```tsx
// BEFORE (❌)
if (isLoading) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Loader />
    </div>
  );
}

// AFTER (✅)
if (isLoading) {
  return (
    <div className="flex items-center justify-center">
      <Loader />
    </div>
  );
}
```

### Step 4: Fix Modal and Dialog Components
**For modal/dialog components:**

```tsx
// ✅ Keep these - they're appropriate for modals
max-h-[95vh]
min-h-[60vh]
```

### Step 5: Fix Navigation Components
**Ensure navigation components check authentication:**

```tsx
// ✅ Add to both desktop and mobile navigation
const { user, isLoading } = useUser();

if (isLoading || !user) return null;
```

## 📋 **Checklist for Layout Consistency**

### ✅ **Root Layout (app/layout.tsx)**
- [ ] Uses `min-h-screen` for app root
- [ ] Has proper flex structure with navbar + main
- [ ] Main content has `overflow-hidden`
- [ ] Mobile navigation is outside main flow

### ✅ **Protected Layout (app/(protected)/layout.tsx)**
- [ ] Uses unified padding: `p-6 pb-24 md:pb-6`
- [ ] No conflicting height calculations
- [ ] Proper flex structure

### ✅ **Page Components**
- [ ] No `min-h-[calc(100vh-*)]` patterns
- [ ] No `md:pl-*` patterns
- [ ] Use consistent padding system
- [ ] Proper centering when needed

### ✅ **Component Loading States**
- [ ] No `min-h-screen` in component loading states
- [ ] Simple centering without height constraints
- [ ] Consistent with page layout

### ✅ **Navigation Components**
- [ ] Check user authentication before rendering
- [ ] Proper sticky positioning
- [ ] Correct z-index layering

### ✅ **Modal/Dialog Components**
- [ ] Use viewport-relative heights (`95vh`, `60vh`)
- [ ] Proper overflow handling
- [ ] Correct positioning

## 🎯 **Best Practices**

1. **Single Source of Truth**: Root layout manages overall structure
2. **Consistent Padding**: Use `p-6 pb-24 md:pb-6` for all protected pages
3. **Component Separation**: Components shouldn't manage page layout
4. **Authentication Awareness**: Navigation components check auth status
5. **Responsive Design**: Use proper breakpoint patterns
6. **Overflow Control**: Maintain `overflow-hidden` on content areas

## 🔄 **Verification Process**

1. **Build Check**: `npm run build` - should pass without errors
2. **Layout Test**: Check each page renders correctly
3. **Navigation Test**: Verify navbar/mobile nav work properly
4. **Responsive Test**: Test on mobile and desktop
5. **Authentication Test**: Verify layout works with/without user

This template ensures all pages and components follow the unified layout system consistently.