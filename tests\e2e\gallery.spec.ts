import { test, expect } from "@playwright/test";

test.describe("Gallery Page", () => {
  test("should render gallery header and filters", async ({ page }) => {
    await page.goto("/gallery");
    // Wait for either the header or the empty state to appear
    const header = page.locator(".gradient-text");
    const empty = page.getByText("No images found");
    await Promise.race([
      header.waitFor({ state: "visible", timeout: 5000 }),
      empty.waitFor({ state: "visible", timeout: 5000 }),
    ]);
    if (await header.isVisible().catch(() => false)) {
      await expect(header).toBeVisible();
      await expect(page.locator("#gallery-style-filter")).toBeVisible();
      await expect(page.locator("#gallery-share-filter")).toBeVisible();
    } else {
      await expect(empty).toBeVisible();
    }
  });

  test("should show gallery grid or empty state", async ({ page }) => {
    await page.goto("/gallery");
    const grid = page.locator('[data-testid="gallery-grid"]');
    const empty = page.getByText("No images found");
    if (await grid.isVisible({ timeout: 3000 })) {
      await expect(grid).toBeVisible();
    } else {
      await expect(empty).toBeVisible();
    }
  });

  test("should have correct SEO meta tags", async ({ page }) => {
    await page.goto("/gallery");
    await expect(page).toHaveTitle("PxlMorph AI - AI-Powered Image Generation");
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      "content",
      /Create stunning AI-generated images with advanced style controls and real-time editing capabilities\./i
    );
  });
});
