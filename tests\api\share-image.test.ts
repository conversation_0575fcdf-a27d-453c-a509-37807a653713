// @ts-nocheck
import { shareImageHandler } from "@/lib/api/shareImageHandler";
import { <PERSON><PERSON><PERSON> } from "buffer";

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock Supabase
const mockUpdate = jest.fn(() => ({
  eq: jest.fn(() => Promise.resolve({ error: null })),
}));
const mockSelect = jest.fn().mockReturnThis();
const mockEq = jest.fn().mockReturnThis();
const mockSingle = jest.fn(() => Promise.resolve({ data: { user_id: "user1" }, error: null }));
const mockFrom = jest.fn(() => ({
  update: mockUpdate,
  select: mockSelect,
  eq: mockEq,
  single: mockSingle,
}));
const mockAuthGetUser = jest.fn(() => Promise.resolve({ data: { user: { id: "user1" } }, error: null }));
const mockCreateClient = () => ({
  from: mockFrom,
  auth: {
    getUser: mockAuthGetUser,
  },
});

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("shareImageHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUpdate.mockImplementation(() => ({
      eq: jest.fn(() => Promise.resolve({ error: null })),
    }));
  });

  it("returns 400 if imageId or isShared is missing", async () => {
    const { status, body } = await shareImageHandler({
      headers: {},
      body: { imageId: "", isShared: undefined },
      env: {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      },
    });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Missing or invalid parameters/);
  });

  it("returns 200 on success", async () => {
    mockUpdate.mockImplementationOnce(() => ({
      eq: jest.fn(() => Promise.resolve({ error: null })),
    }));
    const { status, body } = await shareImageHandler({
      headers: { authorization: "Bearer test_token" },
      body: { imageId: "img1", isShared: true },
      env: {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      },
    });
    console.log("shareImageHandler success:", { status, body });
    expect(status).toBe(200);
    expect(body.success).toBe(true);
    expect(mockFrom).toHaveBeenCalledWith("images");
    expect(mockUpdate).toHaveBeenCalled();
  });

  it("returns 500 on DB error", async () => {
    mockUpdate.mockImplementationOnce(() => ({
      eq: jest.fn(() => Promise.resolve({ error: { message: "DB error" } })),
    }));
    const { status, body } = await shareImageHandler({
      headers: { authorization: "Bearer test_token" },
      body: { imageId: "img1", isShared: false },
      env: {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      },
    });
    console.log("shareImageHandler DB error:", { status, body });
    expect(status).toBe(500);
    expect(body.error).toMatch(/DB error|Server error/);
  });
});
