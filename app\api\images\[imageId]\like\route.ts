import { NextRequest, NextResponse } from "next/server";
import { imageLikeHandler } from "@/lib/api/imageLikeHandler";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ imageId: string }> }
) {
  const { imageId } = await params;
  const { status, body } = await imageLikeHandler({
    imageId,
    headers: request.headers,
  });
  return NextResponse.json(body, { status });
}
