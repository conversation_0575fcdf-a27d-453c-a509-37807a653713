"use client";

import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { useAnalytics } from "../../lib/hooks/useAnalytics";

interface PageViewTrackerProps {
  children: React.ReactNode;
}

export const PageViewTracker: React.FC<PageViewTrackerProps> = ({
  children,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { trackPageView } = useAnalytics();

  useEffect(() => {
    // Track page view when pathname or search params change
    const pageName = pathname || "/";
    const queryString = searchParams?.toString() || "";

    trackPageView(pageName, {
      query_string: queryString,
      full_path: queryString ? `${pageName}?${queryString}` : pageName,
    });
  }, [pathname, searchParams, trackPageView]);

  return <>{children}</>;
};
