// @ts-nocheck
import { deleteImageHandler } from "../../lib/api/deleteImageHandler";

jest.mock("@/lib/supabaseClient", () => {
  const mockClient = jest.fn(() => ({
    auth: { getUser: jest.fn(() => Promise.resolve({ data: { user: { id: "user123" } }, error: null })) },
    from: jest.fn(() => ({
      delete: jest.fn(() => ({ eq: jest.fn().mockReturnThis() })),
      select: jest.fn(() => ({ eq: jest.fn().mockReturnThis() })),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(() => Promise.resolve({ data: { id: "img1" }, error: null })),
    })),
  }));
  return {
    __esModule: true,
    default: mockClient,
    supabaseClient: mockClient,
  };
});

describe("deleteImageHandler", () => {
  const env = {
    NEXT_PUBLIC_SUPABASE_URL: "http://localhost:54321",
    SUPABASE_SERVICE_ROLE_KEY: "key",
  };
  const validUuid = "123e4567-e89b-12d3-a456-************";
  const mockSupabase = {
    auth: {
      getUser: jest.fn(async () => ({
        data: { user: { id: "user1" } },
        error: null,
      })),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(async () => ({
        data: {
          id: validUuid,
          user_id: "user1",
          image_url: "private-images/user1/file.png",
        },
        error: null,
      })),
      delete: jest.fn().mockReturnThis(),
    })),
    storage: {
      from: jest.fn(() => ({
        remove: jest.fn(() => Promise.resolve({ error: null })),
      })),
    },
  };

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockSupabase),
}));
  it("returns 401 if no auth header", async () => {
    const result = await deleteImageHandler({
      headers: {},
      body: { imageId: validUuid },
      env,
      supabaseClient: mockSupabase,
    });
    expect(result.status).toBe(401);
    expect(result.body.error).toMatch(/Authentication required/);
  });
  const mockSupabaseClient = jest.fn(() => {
    const singleFn = jest.fn(() => Promise.resolve({ data: { id: "img1", user_id: "user123", image_url: "private-images/user123/file.png" }, error: null }));
    const eqFn = jest.fn(() => ({ single: singleFn }));
    const selectFn = jest.fn(() => ({ eq: eqFn }));
    const deleteFn = jest.fn(() => ({ eq: eqFn }));
    const fromFn = jest.fn(() => ({ select: selectFn, delete: deleteFn, eq: eqFn }));
    return {
      auth: { getUser: jest.fn(() => Promise.resolve({ data: { user: { id: "user123" } }, error: null })) },
      from: fromFn,
      storage: {
        from: jest.fn(() => ({
          remove: jest.fn(() => Promise.resolve({ error: null })),
        })),
      },
    };
  });
  it("returns 400 if imageId is missing", async () => {
    const result = await deleteImageHandler({
      headers: { authorization: "Bearer validtoken" },
      body: {},
      env: { NEXT_PUBLIC_SUPABASE_URL: "url", SUPABASE_SERVICE_ROLE_KEY: "key" },
      supabaseClient: mockSupabaseClient,
    });
    expect(result.status).toBe(400);
    expect(result.body.error).toMatch(/Image ID is required/);
  });
  it("returns 400 if imageId is not a valid UUID", async () => {
    const result = await deleteImageHandler({
      headers: { authorization: "Bearer validtoken" },
      body: { imageId: "not-a-uuid" },
      env: { NEXT_PUBLIC_SUPABASE_URL: "url", SUPABASE_SERVICE_ROLE_KEY: "key" },
      supabaseClient: mockSupabaseClient,
    });
    expect(result.status).toBe(400);
    expect(result.body.error).toMatch(/Invalid Image ID format/);
  });
  it("returns 200 if valid", async () => {
    const result = await deleteImageHandler({
      headers: { authorization: "Bearer validtoken" },
      body: { imageId: validUuid },
      env: { NEXT_PUBLIC_SUPABASE_URL: "url", SUPABASE_SERVICE_ROLE_KEY: "key" },
      supabaseClient: mockSupabaseClient,
    });
    expect(result.status).toBe(200);
    expect(result.body.success).toBe(true);
  });
});
