// @ts-nocheck
import { imageLikeHandler } from "@/lib/api/imageLikeHandler";
import { Buffer } from "buffer";

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-role-key";

// Mock Supabase
const mockGetUser = jest.fn(() =>
  Promise.resolve({ data: { user: { id: "user123" } }, error: null }),
);
const mockMaybeSingle = jest.fn(() =>
  Promise.resolve({ data: null, error: null }),
);
const mockSingle = jest.fn(() =>
  Promise.resolve({ data: { like_count: 1 }, error: null }),
);
const mockEq2 = jest.fn(() => ({
  maybeSingle: mockMaybeSingle,
  single: mockSingle,
}));
const mockEq1 = jest.fn(() => ({
  eq: mockEq2,
  maybeSingle: mockMaybeSingle,
  single: mockSingle,
}));
const mockSelect = jest.fn(() => ({ eq: mockEq1 }));
const mockInsert = jest.fn(() => Promise.resolve({ error: null }));
const mockDelete = jest.fn(() => ({ eq: jest.fn().mockReturnThis() }));
const mockFrom = jest.fn(() => ({
  select: mockSelect,
  eq: mockEq1,
  insert: mockInsert,
  delete: mockDelete,
  single: mockSingle,
}));
const mockCreateClient = () => ({
  auth: { getUser: mockGetUser },
  from: mockFrom,
});

jest.mock("@supabase/supabase-js", () => ({
  __esModule: true,
  createClient: jest.fn(() => mockCreateClient()),
}));

describe("imageLikeHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns 401 if no auth header", async () => {
    const headers = new Headers();
    const { status, body } = await imageLikeHandler({
      imageId: "uuid",
      headers,
    });
    expect(status).toBe(401);
    expect(body.error).toMatch(/Unauthorized/);
  });

  it("returns 400 if imageId is missing", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await imageLikeHandler({ imageId: "", headers });
    expect(status).toBe(400);
    expect(body.error).toMatch(/Image ID is required/);
  });

  it("returns 200 and liked=true on like success", async () => {
    const headers = new Headers({ authorization: "Bearer validtoken" });
    const { status, body } = await imageLikeHandler({
      imageId: "img1",
      headers,
    });
    console.log("imageLikeHandler like success:", { status, body });
    expect(status).toBe(200);
    expect(body.liked).toBe(true);
    expect(body.like_count).toBe(1);
  });
});
