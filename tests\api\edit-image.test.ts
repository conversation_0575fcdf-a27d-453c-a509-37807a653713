// @ts-nocheck
import { editImageHandler } from "../../lib/api/editImageHandler";

describe("editImageHandler", () => {
  const env = {
    NEXT_PUBLIC_SUPABASE_URL: "url",
    SUPABASE_SERVICE_ROLE_KEY: "key",
  };
  const mockOpenAI = {
    images: {
      edit: jest.fn(async () => ({ data: [{ url: "http://image.url" }] })),
    },
  };
  const mockSupabase = {
    auth: {
      getUser: jest.fn(async () => ({
        data: { user: { id: "user1" } },
        error: null,
      })),
    },
  };
  it("returns 401 if no auth header", async () => {
    const result = await editImageHandler({
      headers: {},
      formData: {},
      env,
      openai: mockOpenAI,
      supabaseClient: mockSupabase,
    });
    expect(result.status).toBe(401);
    expect(result.body.error).toMatch(/Authentication required/);
  });
  it("returns 400 if prompt or image is missing", async () => {
    const result = await editImageHandler({
      headers: { authorization: "Bearer validtoken" },
      formData: {},
      env,
      openai: mockOpenAI,
      supabaseClient: mockSupabase,
    });
    expect(result.status).toBe(400);
    expect(result.body.error).toMatch(/Missing prompt or image file/);
  });
  it("returns 200 and url if valid", async () => {
    const result = await editImageHandler({
      headers: { authorization: "Bearer validtoken" },
      formData: { prompt: "test", image: {} },
      env,
      openai: mockOpenAI,
      supabaseClient: mockSupabase,
    });
    expect(result.status).toBe(200);
    expect(result.body.url).toBe("http://image.url");
  });
});
